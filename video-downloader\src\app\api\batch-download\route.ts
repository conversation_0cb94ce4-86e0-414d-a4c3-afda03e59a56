import { NextRequest, NextResponse } from 'next/server';
import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';

// Store active batch downloads
const activeBatchDownloads = new Map<string, any>();

export async function POST(request: NextRequest) {
  try {
    const { urls, options } = await request.json();

    if (!urls || !Array.isArray(urls) || urls.length === 0) {
      return NextResponse.json({ error: 'URLs array is required' }, { status: 400 });
    }

    // Validate URLs
    const validUrls = urls.filter(url => {
      try {
        new URL(url);
        return true;
      } catch {
        return false;
      }
    });

    if (validUrls.length === 0) {
      return NextResponse.json({ error: 'No valid URLs provided' }, { status: 400 });
    }

    // Generate unique batch download ID
    const batchId = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    
    // Create downloads directory if it doesn't exist
    const downloadsDir = path.join(process.cwd(), 'public', 'downloads');
    if (!fs.existsSync(downloadsDir)) {
      fs.mkdirSync(downloadsDir, { recursive: true });
    }

    // Build yt-dlp command for batch download
    let command = ['python', '-m', 'yt_dlp'];
    
    // Handle audio extraction
    if (options.type === 'audio') {
      command.push('--extract-audio');
      if (options.audioFormat) {
        command.push('--audio-format', options.audioFormat);
      }
      command.push('--audio-quality', '0'); // Best quality
      command.push('-f', 'bestaudio');
    } else {
      // Add format selection for video
      if (options.quality) {
        command.push('-f', `best[height<=${options.quality}]`);
      } else {
        command.push('-f', 'best');
      }
    }

    // Add output template
    const outputTemplate = `${batchId}_%(playlist_index)s_%(title)s.%(ext)s`;
    command.push('-o', path.join(downloadsDir, outputTemplate));
    
    // Add progress hook
    command.push('--newline');
    
    // Add all URLs
    command.push(...validUrls);

    console.log('Executing batch command:', command.join(' '));

    // Start batch download process
    const downloadProcess = spawn('python', command.slice(1), {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // Store batch download info
    activeBatchDownloads.set(batchId, {
      process: downloadProcess,
      status: 'downloading',
      progress: 0,
      urls: validUrls,
      startTime: Date.now(),
      totalItems: 0,
      completedItems: 0,
      currentItem: '',
      files: []
    });

    let currentProgress = 0;
    let totalItems = validUrls.length;
    let completedItems = 0;
    const downloadedFiles: string[] = [];

    // Handle stdout for progress tracking
    downloadProcess.stdout?.on('data', (data) => {
      const output = data.toString();
      console.log('yt-dlp batch stdout:', output);
      
      const batchInfo = activeBatchDownloads.get(batchId);
      if (!batchInfo) return;

      // Extract filename from output
      const filenameMatch = output.match(/\[download\] Destination: (.+)/);
      if (filenameMatch) {
        const filename = path.basename(filenameMatch[1]);
        batchInfo.currentItem = filename;
      }

      // Extract progress from output
      const progressMatch = output.match(/\[download\]\s+(\d+\.?\d*)%/);
      if (progressMatch) {
        currentProgress = parseFloat(progressMatch[1]);
        batchInfo.progress = ((completedItems * 100) + currentProgress) / totalItems;
      }

      // Check for completion of individual item
      const completionMatch = output.match(/\[download\] 100%/);
      if (completionMatch) {
        completedItems++;
        if (batchInfo.currentItem) {
          downloadedFiles.push(batchInfo.currentItem);
          batchInfo.files = [...downloadedFiles];
        }
        batchInfo.completedItems = completedItems;
        batchInfo.progress = (completedItems * 100) / totalItems;
      }

      // Update playlist info if available
      const playlistMatch = output.match(/\[download\] Downloading (\d+) videos/);
      if (playlistMatch) {
        totalItems = parseInt(playlistMatch[1]);
        batchInfo.totalItems = totalItems;
      }
    });

    // Handle stderr
    downloadProcess.stderr?.on('data', (data) => {
      console.error('yt-dlp batch stderr:', data.toString());
    });

    // Handle process completion
    downloadProcess.on('close', (code) => {
      const batchInfo = activeBatchDownloads.get(batchId);
      if (batchInfo) {
        if (code === 0) {
          batchInfo.status = 'completed';
          batchInfo.progress = 100;
        } else {
          batchInfo.status = 'failed';
          batchInfo.error = `Process exited with code ${code}`;
        }
      }
    });

    // Handle process error
    downloadProcess.on('error', (error) => {
      console.error('Batch download process error:', error);
      const batchInfo = activeBatchDownloads.get(batchId);
      if (batchInfo) {
        batchInfo.status = 'failed';
        batchInfo.error = error.message;
      }
    });

    return NextResponse.json({
      success: true,
      batchId: batchId,
      message: `Batch download started for ${validUrls.length} URL(s)`,
      totalUrls: validUrls.length
    });

  } catch (error: any) {
    console.error('Batch download API error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const batchId = searchParams.get('id');

  if (!batchId) {
    return NextResponse.json({ error: 'Batch ID is required' }, { status: 400 });
  }

  const batchInfo = activeBatchDownloads.get(batchId);
  
  if (!batchInfo) {
    return NextResponse.json({ error: 'Batch download not found' }, { status: 404 });
  }

  return NextResponse.json({
    success: true,
    data: {
      id: batchId,
      status: batchInfo.status,
      progress: batchInfo.progress,
      urls: batchInfo.urls,
      startTime: batchInfo.startTime,
      totalItems: batchInfo.totalItems,
      completedItems: batchInfo.completedItems,
      currentItem: batchInfo.currentItem,
      files: batchInfo.files,
      error: batchInfo.error || null
    }
  });
}
