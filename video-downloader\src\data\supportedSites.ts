export interface SiteInfo {
  name: string;
  domain: string;
  icon: string;
  category: string;
  features: string[];
  examples: string[];
  description: string;
  supportsPlaylists: boolean;
  supportsLive: boolean;
  supportsSubtitles: boolean;
  maxQuality: string;
  audioFormats: string[];
  notes?: string;
}

export const supportedSites: SiteInfo[] = [
  // Video Platforms
  {
    name: "YouTube",
    domain: "youtube.com",
    icon: "🎥",
    category: "Video Platforms",
    features: ["4K/8K Video", "Live Streams", "Playlists", "Subtitles", "Audio Extraction"],
    examples: ["https://youtube.com/watch?v=...", "https://youtube.com/playlist?list=..."],
    description: "World's largest video platform with comprehensive format support",
    supportsPlaylists: true,
    supportsLive: true,
    supportsSubtitles: true,
    maxQuality: "8K",
    audioFormats: ["mp3", "m4a", "opus", "wav"]
  },
  {
    name: "Vimeo",
    domain: "vimeo.com",
    icon: "🎬",
    category: "Video Platforms",
    features: ["HD Video", "Private Videos", "Password Protected", "Subtitles"],
    examples: ["https://vimeo.com/123456789"],
    description: "High-quality video platform for creators and professionals",
    supportsPlaylists: true,
    supportsLive: false,
    supportsSubtitles: true,
    maxQuality: "4K",
    audioFormats: ["mp3", "m4a"]
  },
  {
    name: "Dailymotion",
    domain: "dailymotion.com",
    icon: "📺",
    category: "Video Platforms",
    features: ["HD Video", "Playlists", "Live Streams"],
    examples: ["https://dailymotion.com/video/..."],
    description: "European video platform with diverse content",
    supportsPlaylists: true,
    supportsLive: true,
    supportsSubtitles: false,
    maxQuality: "1080p",
    audioFormats: ["mp3", "m4a"]
  },

  // Social Media
  {
    name: "TikTok",
    domain: "tiktok.com",
    icon: "🎵",
    category: "Social Media",
    features: ["Short Videos", "Audio Extraction", "User Profiles"],
    examples: ["https://tiktok.com/@user/video/..."],
    description: "Short-form video platform with viral content",
    supportsPlaylists: false,
    supportsLive: false,
    supportsSubtitles: false,
    maxQuality: "1080p",
    audioFormats: ["mp3", "m4a"]
  },
  {
    name: "Instagram",
    domain: "instagram.com",
    icon: "📸",
    category: "Social Media",
    features: ["Posts", "Stories", "Reels", "IGTV"],
    examples: ["https://instagram.com/p/...", "https://instagram.com/stories/..."],
    description: "Photo and video sharing with stories and reels",
    supportsPlaylists: false,
    supportsLive: true,
    supportsSubtitles: false,
    maxQuality: "1080p",
    audioFormats: ["mp3", "m4a"]
  },
  {
    name: "Twitter/X",
    domain: "twitter.com",
    icon: "🐦",
    category: "Social Media",
    features: ["Video Tweets", "Spaces Audio", "Live Streams"],
    examples: ["https://twitter.com/user/status/..."],
    description: "Social media platform with video and audio content",
    supportsPlaylists: false,
    supportsLive: true,
    supportsSubtitles: false,
    maxQuality: "1080p",
    audioFormats: ["mp3", "m4a"]
  },
  {
    name: "Facebook",
    domain: "facebook.com",
    icon: "👥",
    category: "Social Media",
    features: ["Video Posts", "Live Streams", "Stories"],
    examples: ["https://facebook.com/watch/..."],
    description: "Social network with extensive video content",
    supportsPlaylists: false,
    supportsLive: true,
    supportsSubtitles: false,
    maxQuality: "1080p",
    audioFormats: ["mp3", "m4a"]
  },

  // Streaming Platforms
  {
    name: "Twitch",
    domain: "twitch.tv",
    icon: "🎮",
    category: "Streaming",
    features: ["Live Streams", "VODs", "Clips", "Chat Replay"],
    examples: ["https://twitch.tv/videos/...", "https://clips.twitch.tv/..."],
    description: "Gaming and live streaming platform",
    supportsPlaylists: false,
    supportsLive: true,
    supportsSubtitles: false,
    maxQuality: "1080p",
    audioFormats: ["mp3", "m4a"]
  },
  {
    name: "Kick",
    domain: "kick.com",
    icon: "⚡",
    category: "Streaming",
    features: ["Live Streams", "VODs", "Clips"],
    examples: ["https://kick.com/..."],
    description: "Alternative streaming platform",
    supportsPlaylists: false,
    supportsLive: true,
    supportsSubtitles: false,
    maxQuality: "1080p",
    audioFormats: ["mp3", "m4a"]
  },

  // News & Media
  {
    name: "BBC iPlayer",
    domain: "bbc.co.uk",
    icon: "📻",
    category: "News & Media",
    features: ["TV Shows", "News", "Documentaries", "Subtitles"],
    examples: ["https://bbc.co.uk/iplayer/episode/..."],
    description: "BBC's streaming service with UK content",
    supportsPlaylists: true,
    supportsLive: true,
    supportsSubtitles: true,
    maxQuality: "1080p",
    audioFormats: ["mp3", "m4a"],
    notes: "May require UK IP address"
  },
  {
    name: "CNN",
    domain: "cnn.com",
    icon: "📰",
    category: "News & Media",
    features: ["News Videos", "Live Streams", "Documentaries"],
    examples: ["https://cnn.com/videos/..."],
    description: "News network with video content",
    supportsPlaylists: false,
    supportsLive: true,
    supportsSubtitles: true,
    maxQuality: "1080p",
    audioFormats: ["mp3", "m4a"]
  },

  // Educational
  {
    name: "Khan Academy",
    domain: "khanacademy.org",
    icon: "🎓",
    category: "Educational",
    features: ["Educational Videos", "Courses", "Subtitles"],
    examples: ["https://khanacademy.org/..."],
    description: "Free educational content and courses",
    supportsPlaylists: true,
    supportsLive: false,
    supportsSubtitles: true,
    maxQuality: "1080p",
    audioFormats: ["mp3", "m4a"]
  },
  {
    name: "Coursera",
    domain: "coursera.org",
    icon: "📚",
    category: "Educational",
    features: ["Course Videos", "Lectures", "Subtitles"],
    examples: ["https://coursera.org/learn/..."],
    description: "Online learning platform with university courses",
    supportsPlaylists: true,
    supportsLive: false,
    supportsSubtitles: true,
    maxQuality: "1080p",
    audioFormats: ["mp3", "m4a"],
    notes: "May require enrollment"
  },

  // Music & Audio
  {
    name: "SoundCloud",
    domain: "soundcloud.com",
    icon: "🎧",
    category: "Music & Audio",
    features: ["Audio Tracks", "Playlists", "Podcasts"],
    examples: ["https://soundcloud.com/user/track"],
    description: "Audio platform for music and podcasts",
    supportsPlaylists: true,
    supportsLive: false,
    supportsSubtitles: false,
    maxQuality: "Audio Only",
    audioFormats: ["mp3", "m4a", "opus"]
  },
  {
    name: "Bandcamp",
    domain: "bandcamp.com",
    icon: "🎼",
    category: "Music & Audio",
    features: ["Music Albums", "High Quality Audio", "Artist Pages"],
    examples: ["https://artist.bandcamp.com/album/..."],
    description: "Independent music platform with high-quality audio",
    supportsPlaylists: true,
    supportsLive: false,
    supportsSubtitles: false,
    maxQuality: "Audio Only",
    audioFormats: ["mp3", "flac", "wav"]
  },

  // International Platforms
  {
    name: "Bilibili",
    domain: "bilibili.com",
    icon: "📱",
    category: "International",
    features: ["Anime", "Gaming", "Live Streams", "Subtitles"],
    examples: ["https://bilibili.com/video/..."],
    description: "Chinese video platform popular for anime and gaming",
    supportsPlaylists: true,
    supportsLive: true,
    supportsSubtitles: true,
    maxQuality: "4K",
    audioFormats: ["mp3", "m4a"]
  },
  {
    name: "Niconico",
    domain: "nicovideo.jp",
    icon: "🇯🇵",
    category: "International",
    features: ["Japanese Content", "Live Streams", "Comments"],
    examples: ["https://nicovideo.jp/watch/..."],
    description: "Japanese video platform with unique comment system",
    supportsPlaylists: true,
    supportsLive: true,
    supportsSubtitles: false,
    maxQuality: "1080p",
    audioFormats: ["mp3", "m4a"]
  },

  // Adult Content (18+)
  {
    name: "Adult Platforms",
    domain: "various",
    icon: "🔞",
    category: "Adult Content",
    features: ["Various adult video platforms"],
    examples: ["Multiple adult video sites supported"],
    description: "Various adult content platforms (18+ only)",
    supportsPlaylists: true,
    supportsLive: true,
    supportsSubtitles: false,
    maxQuality: "4K",
    audioFormats: ["mp3", "m4a"],
    notes: "Age verification required"
  }
];

export const siteCategories = [
  "Video Platforms",
  "Social Media", 
  "Streaming",
  "News & Media",
  "Educational",
  "Music & Audio",
  "International",
  "Adult Content"
];

export const getTotalSitesCount = () => {
  // yt-dlp supports 1000+ sites, but we're showcasing the most popular ones
  return "1000+";
};

export const findSiteByDomain = (url: string): SiteInfo | null => {
  try {
    const domain = new URL(url).hostname.replace('www.', '');
    return supportedSites.find(site => 
      domain.includes(site.domain.replace('www.', '')) ||
      site.domain.replace('www.', '').includes(domain)
    ) || null;
  } catch {
    return null;
  }
};
