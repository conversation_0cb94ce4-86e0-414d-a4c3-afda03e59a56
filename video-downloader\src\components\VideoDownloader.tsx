'use client';

import { useState, useEffect } from 'react';
import VideoInfoCard from './VideoInfoCard';
import DownloadProgress from './DownloadProgress';
import DownloadHistory from './DownloadHistory';
import BatchDownloader from './BatchDownloader';
import SettingsPanel from './SettingsPanel';
import DownloadQueue from './DownloadQueue';
import SiteDirectory from './SiteDirectory';
import URLValidator from './URLValidator';
import PlatformSpecificOptions from './PlatformSpecificOptions';
import { SiteInfo } from '@/data/supportedSites';

interface VideoInfo {
  title: string;
  description: string;
  duration: number;
  uploader: string;
  upload_date: string;
  view_count: number;
  thumbnail: string;
  formats: Array<{
    format_id: string;
    ext: string;
    quality: string;
    filesize: number;
    format_note: string;
    vcodec: string;
    acodec: string;
    fps: number;
    resolution: string;
  }>;
  url: string;
  webpage_url: string;
  extractor: string;
}

interface DownloadStatus {
  id: string;
  status: 'downloading' | 'completed' | 'failed';
  progress: number;
  filename: string | null;
  url: string;
  startTime: number;
  error: string | null;
}

export default function VideoDownloader() {
  const [url, setUrl] = useState('');
  const [videoInfo, setVideoInfo] = useState<VideoInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [activeDownloads, setActiveDownloads] = useState<DownloadStatus[]>([]);
  const [downloadHistory, setDownloadHistory] = useState<any[]>([]);
  const [activeBatchDownloads, setActiveBatchDownloads] = useState<any[]>([]);
  const [showSettings, setShowSettings] = useState(false);
  const [downloadQueue, setDownloadQueue] = useState<any[]>([]);
  const [detectedSite, setDetectedSite] = useState<SiteInfo | null>(null);
  const [platformOptions, setPlatformOptions] = useState<any>({});
  const [settings, setSettings] = useState<any>({
    maxConcurrentDownloads: 3,
    defaultQuality: 'best',
    defaultAudioFormat: 'mp3',
    autoSubtitles: false,
    defaultSubtitleLanguages: 'en'
  });

  const fetchVideoInfo = async () => {
    if (!url.trim()) {
      setError('Please enter a valid URL');
      return;
    }

    setLoading(true);
    setError('');
    setVideoInfo(null);

    try {
      const response = await fetch('/api/video-info', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: url.trim() }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch video information');
      }

      if (data.success) {
        setVideoInfo(data.data);
      } else {
        throw new Error(data.error || 'Unknown error occurred');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch video information');
    } finally {
      setLoading(false);
    }
  };

  const startDownload = async (format?: string, quality?: string, type?: string, audioFormat?: string, subtitleOptions?: any) => {
    if (!videoInfo) return;

    try {
      const response = await fetch('/api/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: videoInfo.url,
          format,
          quality,
          type,
          audioFormat,
          subtitleOptions,
          platformOptions,
          siteInfo: detectedSite,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to start download');
      }

      if (data.success) {
        // Add to active downloads
        const newDownload: DownloadStatus = {
          id: data.downloadId,
          status: 'downloading',
          progress: 0,
          filename: null,
          url: videoInfo.url,
          startTime: Date.now(),
          error: null,
        };

        setActiveDownloads(prev => [...prev, newDownload]);
        
        // Start polling for progress
        pollDownloadProgress(data.downloadId);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to start download');
    }
  };

  const pollDownloadProgress = async (downloadId: string) => {
    const poll = async () => {
      try {
        const response = await fetch(`/api/download?id=${downloadId}`);
        const data = await response.json();

        if (data.success) {
          setActiveDownloads(prev =>
            prev.map(download =>
              download.id === downloadId
                ? { ...download, ...data.data }
                : download
            )
          );

          // If download is completed or failed, stop polling
          if (data.data.status === 'completed' || data.data.status === 'failed') {
            if (data.data.status === 'completed') {
              // Refresh download history
              fetchDownloadHistory();
            }
            return;
          }

          // Continue polling
          setTimeout(poll, 2000);
        }
      } catch (err) {
        console.error('Error polling download progress:', err);
      }
    };

    poll();
  };

  const fetchDownloadHistory = async () => {
    try {
      const response = await fetch('/api/downloads');
      const data = await response.json();

      if (data.success) {
        setDownloadHistory(data.data);
      }
    } catch (err) {
      console.error('Error fetching download history:', err);
    }
  };

  const deleteDownload = async (filename: string) => {
    try {
      const response = await fetch('/api/downloads', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ filename }),
      });

      if (response.ok) {
        fetchDownloadHistory();
      }
    } catch (err) {
      console.error('Error deleting download:', err);
    }
  };

  const handleBatchDownload = async (urls: string[], options: any) => {
    try {
      const response = await fetch('/api/batch-download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ urls, options }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to start batch download');
      }

      if (data.success) {
        // Add to active batch downloads
        const newBatchDownload = {
          id: data.batchId,
          status: 'downloading',
          progress: 0,
          urls: urls,
          startTime: Date.now(),
          totalItems: data.totalUrls,
          completedItems: 0,
          currentItem: '',
          files: []
        };

        setActiveBatchDownloads(prev => [...prev, newBatchDownload]);

        // Start polling for progress
        pollBatchDownloadProgress(data.batchId);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to start batch download');
    }
  };

  const pollBatchDownloadProgress = async (batchId: string) => {
    const poll = async () => {
      try {
        const response = await fetch(`/api/batch-download?id=${batchId}`);
        const data = await response.json();

        if (data.success) {
          setActiveBatchDownloads(prev =>
            prev.map(batch =>
              batch.id === batchId
                ? { ...batch, ...data.data }
                : batch
            )
          );

          // If batch is completed or failed, stop polling
          if (data.data.status === 'completed' || data.data.status === 'failed') {
            if (data.data.status === 'completed') {
              // Refresh download history
              fetchDownloadHistory();
            }
            return;
          }

          // Continue polling
          setTimeout(poll, 3000);
        }
      } catch (err) {
        console.error('Error polling batch download progress:', err);
      }
    };

    poll();
  };

  // Fetch download history on component mount
  useEffect(() => {
    fetchDownloadHistory();
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    fetchVideoInfo();
  };

  // Queue Management Functions
  const handlePauseDownload = (id: string) => {
    console.log('Pausing download:', id);
  };

  const handleResumeDownload = (id: string) => {
    console.log('Resuming download:', id);
  };

  const handleCancelDownload = (id: string) => {
    setDownloadQueue(prev => prev.filter(item => item.id !== id));
    setActiveDownloads(prev => prev.filter(item => item.id !== id));
  };

  const handleRetryDownload = (id: string) => {
    console.log('Retrying download:', id);
  };

  const handlePriorityChange = (id: string, priority: 'low' | 'normal' | 'high') => {
    setDownloadQueue(prev =>
      prev.map(item =>
        item.id === id ? { ...item, priority } : item
      )
    );
  };

  const handleQueueReorder = (fromIndex: number, toIndex: number) => {
    setDownloadQueue(prev => {
      const newQueue = [...prev];
      const [removed] = newQueue.splice(fromIndex, 1);
      newQueue.splice(toIndex, 0, removed);
      return newQueue;
    });
  };

  const handleSettingsChange = (newSettings: any) => {
    setSettings(newSettings);
  };

  const handleSiteSelect = (site: SiteInfo) => {
    // When a site is selected from the directory, we could pre-fill some options
    setDetectedSite(site);
  };

  const handleExampleSelect = (exampleUrl: string) => {
    setUrl(exampleUrl);
  };

  const handleURLValidation = (isValid: boolean, siteInfo: SiteInfo | null) => {
    setDetectedSite(siteInfo);
  };

  const handlePlatformOptionsChange = (options: any) => {
    setPlatformOptions(options);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header with Settings */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
            Universal Video Downloader
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Enhanced with advanced features and queue management
          </p>
        </div>
        <button
          onClick={() => setShowSettings(true)}
          className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
        >
          ⚙️ Settings
        </button>
      </div>

      {/* Site Directory */}
      <SiteDirectory
        onSiteSelect={handleSiteSelect}
        onExampleSelect={handleExampleSelect}
      />

      {/* Download Queue */}
      <DownloadQueue
        queue={downloadQueue}
        onPause={handlePauseDownload}
        onResume={handleResumeDownload}
        onCancel={handleCancelDownload}
        onRetry={handleRetryDownload}
        onPriorityChange={handlePriorityChange}
        onReorder={handleQueueReorder}
        maxConcurrent={settings.maxConcurrentDownloads}
      />

      {/* Batch Downloader */}
      <BatchDownloader onBatchDownload={handleBatchDownload} />

      {/* URL Input Form */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="url" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Video URL
            </label>
            <div className="flex gap-2">
              <input
                type="url"
                id="url"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                placeholder="Enter video URL (YouTube, Vimeo, etc.)"
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                disabled={loading}
              />
              <button
                type="submit"
                disabled={loading || !url.trim()}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {loading ? 'Analyzing...' : 'Analyze'}
              </button>
            </div>
          </div>
        </form>

        {/* URL Validator */}
        <URLValidator
          url={url}
          onValidationChange={handleURLValidation}
        />

        {/* Platform Specific Options */}
        {detectedSite && (
          <PlatformSpecificOptions
            siteInfo={detectedSite}
            onOptionsChange={handlePlatformOptionsChange}
          />
        )}

        {error && (
          <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-red-800 dark:text-red-200">{error}</p>
          </div>
        )}
      </div>

      {/* Video Information */}
      {videoInfo && (
        <VideoInfoCard videoInfo={videoInfo} onDownload={startDownload} />
      )}

      {/* Active Downloads */}
      {activeDownloads.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
            Active Downloads
          </h3>
          <div className="space-y-4">
            {activeDownloads.map((download) => (
              <DownloadProgress key={download.id} download={download} />
            ))}
          </div>
        </div>
      )}

      {/* Active Batch Downloads */}
      {activeBatchDownloads.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
            📦 Active Batch Downloads
          </h3>
          <div className="space-y-4">
            {activeBatchDownloads.map((batch) => (
              <div key={batch.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-800 dark:text-white">
                      Batch Download ({batch.completedItems}/{batch.totalItems} items)
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {batch.currentItem || 'Preparing...'}
                    </p>
                  </div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white ${
                    batch.status === 'downloading' ? 'bg-blue-600' :
                    batch.status === 'completed' ? 'bg-green-600' : 'bg-red-600'
                  }`}>
                    {batch.status === 'downloading' ? 'Downloading...' :
                     batch.status === 'completed' ? 'Completed' : 'Failed'}
                  </span>
                </div>

                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      batch.status === 'downloading' ? 'bg-blue-600' :
                      batch.status === 'completed' ? 'bg-green-600' : 'bg-red-600'
                    }`}
                    style={{ width: `${Math.max(0, Math.min(100, batch.progress))}%` }}
                  ></div>
                </div>

                <div className="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400">
                  <span>{batch.progress.toFixed(1)}%</span>
                  <span>{batch.urls.length} URL(s)</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Download History */}
      <DownloadHistory
        downloads={downloadHistory}
        onDelete={deleteDownload}
        onRefresh={fetchDownloadHistory}
      />

      {/* Settings Panel */}
      <SettingsPanel
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        onSettingsChange={handleSettingsChange}
      />
    </div>
  );
}
