import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const expectedRedirectUri = `${baseUrl}/api/auth/callback/google`;
  
  return NextResponse.json({
    message: 'OAuth Redirect URI Test',
    expectedRedirectUri: expectedRedirectUri,
    instructions: [
      '1. Copy the expectedRedirectUri below',
      '2. Go to Google Cloud Console → APIs & Services → Credentials',
      '3. Edit your OAuth 2.0 Client ID',
      '4. In "Authorized redirect URIs", add EXACTLY the URI shown below',
      '5. Remove any other redirect URIs',
      '6. Save and wait 1-2 minutes',
      '7. Test authentication again'
    ],
    copyThis: expectedRedirectUri,
    troubleshooting: {
      commonMistakes: [
        'Using https:// instead of http:// for localhost',
        'Adding trailing slash at the end',
        'Wrong port number',
        'Typos in the path'
      ]
    }
  });
}
