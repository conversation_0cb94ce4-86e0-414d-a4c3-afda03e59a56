'use client';

import { useState } from 'react';

interface BatchDownloadProps {
  onBatchDownload: (urls: string[], options: BatchDownloadOptions) => void;
}

interface BatchDownloadOptions {
  type: 'video' | 'audio';
  audioFormat?: string;
  quality?: string;
  format?: string;
}

export default function BatchDownloader({ onBatchDownload }: BatchDownloadProps) {
  const [urlText, setUrlText] = useState('');
  const [downloadType, setDownloadType] = useState<'video' | 'audio'>('video');
  const [audioFormat, setAudioFormat] = useState('mp3');
  const [quality, setQuality] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Parse URLs from text (one per line)
    const urls = urlText
      .split('\n')
      .map(url => url.trim())
      .filter(url => url.length > 0 && isValidUrl(url));

    if (urls.length === 0) {
      alert('Please enter at least one valid URL');
      return;
    }

    const options: BatchDownloadOptions = {
      type: downloadType,
      audioFormat: downloadType === 'audio' ? audioFormat : undefined,
      quality: downloadType === 'video' ? quality : undefined,
    };

    onBatchDownload(urls, options);
    setUrlText(''); // Clear the text area after submission
  };

  const isValidUrl = (string: string) => {
    try {
      new URL(string);
      return true;
    } catch {
      return false;
    }
  };

  const getUrlCount = () => {
    return urlText
      .split('\n')
      .map(url => url.trim())
      .filter(url => url.length > 0 && isValidUrl(url)).length;
  };

  if (!isExpanded) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
        <button
          onClick={() => setIsExpanded(true)}
          className="w-full flex items-center justify-between text-left"
        >
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
              📦 Batch Download
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Download multiple videos or playlists at once
            </p>
          </div>
          <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
            📦 Batch Download
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-300">
            Enter multiple URLs (one per line) or playlist URLs
          </p>
        </div>
        <button
          onClick={() => setIsExpanded(false)}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
          </svg>
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* URL Input */}
        <div>
          <label htmlFor="batch-urls" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            URLs or Playlist Links
          </label>
          <textarea
            id="batch-urls"
            value={urlText}
            onChange={(e) => setUrlText(e.target.value)}
            placeholder="https://www.youtube.com/watch?v=example1&#10;https://www.youtube.com/watch?v=example2&#10;https://www.youtube.com/playlist?list=example"
            rows={6}
            className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-vertical"
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Valid URLs detected: {getUrlCount()}
          </p>
        </div>

        {/* Download Type */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Download Type
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="batchDownloadType"
                  value="video"
                  checked={downloadType === 'video'}
                  onChange={(e) => setDownloadType(e.target.value as 'video' | 'audio')}
                  className="mr-2"
                />
                <span className="text-sm">🎥 Video</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="batchDownloadType"
                  value="audio"
                  checked={downloadType === 'audio'}
                  onChange={(e) => setDownloadType(e.target.value as 'video' | 'audio')}
                  className="mr-2"
                />
                <span className="text-sm">🎵 Audio Only</span>
              </label>
            </div>
          </div>

          {/* Format/Quality Selection */}
          <div>
            {downloadType === 'audio' ? (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Audio Format
                </label>
                <select
                  value={audioFormat}
                  onChange={(e) => setAudioFormat(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
                >
                  <option value="mp3">MP3</option>
                  <option value="m4a">M4A</option>
                  <option value="wav">WAV</option>
                  <option value="flac">FLAC</option>
                  <option value="ogg">OGG</option>
                </select>
              </div>
            ) : (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Video Quality
                </label>
                <select
                  value={quality}
                  onChange={(e) => setQuality(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
                >
                  <option value="">Best Available</option>
                  <option value="720">720p</option>
                  <option value="480">480p</option>
                  <option value="360">360p</option>
                  <option value="240">240p</option>
                </select>
              </div>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600 dark:text-gray-300">
            {getUrlCount() > 0 && (
              <span>Ready to download {getUrlCount()} item{getUrlCount() !== 1 ? 's' : ''}</span>
            )}
          </div>
          <button
            type="submit"
            disabled={getUrlCount() === 0}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Start Batch Download
          </button>
        </div>
      </form>

      {/* Tips */}
      <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
        <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">💡 Tips:</h4>
        <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
          <li>• Playlist URLs will automatically download all videos in the playlist</li>
          <li>• Each URL should be on a separate line</li>
          <li>• Invalid URLs will be automatically filtered out</li>
          <li>• Large batches may take significant time to complete</li>
        </ul>
      </div>
    </div>
  );
}
