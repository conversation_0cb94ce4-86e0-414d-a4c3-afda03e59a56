import { NextRequest, NextResponse } from 'next/server';
import { exec, spawn } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import fs from 'fs';

const execAsync = promisify(exec);

// Store active downloads
const activeDownloads = new Map<string, any>();

export async function POST(request: NextRequest) {
  try {
    const { url, format, quality, type, audioFormat, subtitleOptions, platformOptions, siteInfo } = await request.json();

    if (!url) {
      return NextResponse.json({ error: 'URL is required' }, { status: 400 });
    }

    // Validate URL format
    try {
      new URL(url);
    } catch {
      return NextResponse.json({ error: 'Invalid URL format' }, { status: 400 });
    }

    // Generate unique download ID
    const downloadId = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    
    // Create downloads directory if it doesn't exist
    const downloadsDir = path.join(process.cwd(), 'public', 'downloads');
    if (!fs.existsSync(downloadsDir)) {
      fs.mkdirSync(downloadsDir, { recursive: true });
    }

    // Build yt-dlp command
    let command = ['python', '-m', 'yt_dlp'];

    // Handle audio extraction
    if (type === 'audio') {
      command.push('--extract-audio');
      if (audioFormat) {
        command.push('--audio-format', audioFormat);
      }
      command.push('--audio-quality', '0'); // Best quality
      command.push('-f', 'bestaudio');
    } else {
      // Add format selection for video
      if (format && format !== 'best') {
        command.push('-f', format);
      } else if (quality) {
        // Select best format with specified quality
        command.push('-f', `best[height<=${quality}]`);
      } else {
        command.push('-f', 'best');
      }
    }

    // Add subtitle options if requested
    if (subtitleOptions?.includeSubtitles) {
      command.push('--write-subs');
      command.push('--write-auto-subs');
      if (subtitleOptions.subtitleLanguages) {
        command.push('--sub-langs', subtitleOptions.subtitleLanguages);
      }
      command.push('--sub-format', 'srt/vtt/best');
    }

    // Add platform-specific options
    if (platformOptions && siteInfo) {
      // YouTube specific options
      if (siteInfo.domain === 'youtube.com') {
        if (platformOptions.writeDescription) command.push('--write-description');
        if (platformOptions.writeComments) command.push('--write-comments');
        if (platformOptions.writeThumbnail) command.push('--write-thumbnail');
        if (platformOptions.writeAnnotations) command.push('--write-annotations');
        if (platformOptions.playlistItems && platformOptions.playlistItems !== 'all') {
          command.push('--playlist-items', platformOptions.playlistItems);
        }
      }

      // TikTok specific options
      if (siteInfo.domain === 'tiktok.com') {
        if (platformOptions.noWatermark) command.push('--no-watermark');
      }

      // Instagram specific options
      if (siteInfo.domain === 'instagram.com') {
        if (platformOptions.highestQuality) command.push('-f', 'best');
      }

      // Twitch specific options
      if (siteInfo.domain === 'twitch.tv') {
        if (platformOptions.downloadChat) command.push('--write-comments');
        if (platformOptions.twitchQuality && platformOptions.twitchQuality !== 'best') {
          command.push('-f', platformOptions.twitchQuality);
        }
      }

      // Generic options
      if (platformOptions.writeMetadata) command.push('--write-info-json');
      if (platformOptions.embedSubs) command.push('--embed-subs');
    }

    // Add output template
    const outputTemplate = type === 'audio'
      ? `${downloadId}_%(title)s.%(ext)s`
      : `${downloadId}_%(title)s.%(ext)s`;
    command.push('-o', path.join(downloadsDir, outputTemplate));

    // Add progress hook
    command.push('--newline');
    
    // Add URL
    command.push(url);

    console.log('Executing command:', command.join(' '));

    // Start download process
    const downloadProcess = spawn('python', command.slice(1), {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // Store download info
    activeDownloads.set(downloadId, {
      process: downloadProcess,
      status: 'downloading',
      progress: 0,
      url: url,
      startTime: Date.now(),
      filename: null
    });

    let filename = null;
    let progress = 0;

    // Handle stdout for progress tracking
    downloadProcess.stdout?.on('data', (data) => {
      const output = data.toString();
      console.log('yt-dlp stdout:', output);
      
      // Extract filename from output
      const filenameMatch = output.match(/\[download\] Destination: (.+)/);
      if (filenameMatch) {
        filename = path.basename(filenameMatch[1]);
        activeDownloads.get(downloadId)!.filename = filename;
      }

      // Extract progress from output
      const progressMatch = output.match(/\[download\]\s+(\d+\.?\d*)%/);
      if (progressMatch) {
        progress = parseFloat(progressMatch[1]);
        activeDownloads.get(downloadId)!.progress = progress;
      }
    });

    // Handle stderr
    downloadProcess.stderr?.on('data', (data) => {
      console.error('yt-dlp stderr:', data.toString());
    });

    // Handle process completion
    downloadProcess.on('close', (code) => {
      const downloadInfo = activeDownloads.get(downloadId);
      if (downloadInfo) {
        if (code === 0) {
          downloadInfo.status = 'completed';
          downloadInfo.progress = 100;
        } else {
          downloadInfo.status = 'failed';
          downloadInfo.error = `Process exited with code ${code}`;
        }
      }
    });

    // Handle process error
    downloadProcess.on('error', (error) => {
      console.error('Download process error:', error);
      const downloadInfo = activeDownloads.get(downloadId);
      if (downloadInfo) {
        downloadInfo.status = 'failed';
        downloadInfo.error = error.message;
      }
    });

    return NextResponse.json({
      success: true,
      downloadId: downloadId,
      message: 'Download started'
    });

  } catch (error: any) {
    console.error('Download API error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const downloadId = searchParams.get('id');

  if (!downloadId) {
    return NextResponse.json({ error: 'Download ID is required' }, { status: 400 });
  }

  const downloadInfo = activeDownloads.get(downloadId);
  
  if (!downloadInfo) {
    return NextResponse.json({ error: 'Download not found' }, { status: 404 });
  }

  return NextResponse.json({
    success: true,
    data: {
      id: downloadId,
      status: downloadInfo.status,
      progress: downloadInfo.progress,
      filename: downloadInfo.filename,
      url: downloadInfo.url,
      startTime: downloadInfo.startTime,
      error: downloadInfo.error || null
    }
  });
}
