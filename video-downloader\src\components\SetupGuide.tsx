'use client';

import { useState } from 'react';

export default function SetupGuide() {
  const [currentStep, setCurrentStep] = useState(1);
  const [isExpanded, setIsExpanded] = useState(false);

  const steps = [
    {
      title: "Create Google Cloud Project",
      description: "Set up a new project in Google Cloud Console",
      details: [
        "Go to https://console.cloud.google.com/",
        "Click 'Select a project' → 'New Project'",
        "Name: 'Video Downloader App'",
        "Click 'Create'"
      ]
    },
    {
      title: "Enable YouTube Data API",
      description: "Enable the YouTube Data API v3 for your project",
      details: [
        "Navigate to 'APIs & Services' → 'Library'",
        "Search for 'YouTube Data API v3'",
        "Click on it and then 'Enable'"
      ]
    },
    {
      title: "Configure OAuth Consent Screen",
      description: "Set up the OAuth consent screen for your application",
      details: [
        "Go to 'APIs & Services' → 'OAuth consent screen'",
        "User Type: External",
        "App name: 'Video Downloader'",
        "User support email: Your email",
        "Add scopes: youtube.readonly, youtube.force-ssl"
      ]
    },
    {
      title: "Create OAuth2 Credentials",
      description: "Generate OAuth2 client ID and secret",
      details: [
        "Go to 'APIs & Services' → 'Credentials'",
        "Click 'Create Credentials' → 'OAuth client ID'",
        "Application type: Web application",
        "Authorized redirect URIs:",
        "  • http://localhost:3000/api/auth/callback/google",
        "Copy the Client ID and Client Secret"
      ]
    },
    {
      title: "Update Environment Variables",
      description: "Add your credentials to the .env.local file",
      details: [
        "Open video-downloader/.env.local",
        "Replace GOOGLE_CLIENT_ID with your actual Client ID",
        "Replace GOOGLE_CLIENT_SECRET with your actual Client Secret",
        "Generate a secure NEXTAUTH_SECRET (random string)",
        "Restart the development server"
      ]
    }
  ];

  if (!isExpanded) {
    return (
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-800 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4 text-yellow-600 dark:text-yellow-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div>
              <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                ⚙️ YouTube Authentication Setup Required
              </h3>
              <p className="text-xs text-yellow-700 dark:text-yellow-300">
                OAuth credentials need to be configured to enable YouTube login
              </p>
            </div>
          </div>
          <button
            onClick={() => setIsExpanded(true)}
            className="px-3 py-1 text-xs bg-yellow-600 text-white rounded hover:bg-yellow-700 transition-colors"
          >
            Setup Guide
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
          🔧 YouTube Authentication Setup Guide
        </h3>
        <button
          onClick={() => setIsExpanded(false)}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div className="space-y-6">
        {/* Progress Indicator */}
        <div className="flex items-center space-x-2 mb-6">
          {steps.map((_, index) => (
            <div key={index} className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                index + 1 <= currentStep 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300'
              }`}>
                {index + 1}
              </div>
              {index < steps.length - 1 && (
                <div className={`w-8 h-0.5 ${
                  index + 1 < currentStep ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-600'
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Current Step */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <h4 className="text-lg font-medium text-blue-800 dark:text-blue-200 mb-2">
            Step {currentStep}: {steps[currentStep - 1].title}
          </h4>
          <p className="text-blue-700 dark:text-blue-300 mb-4">
            {steps[currentStep - 1].description}
          </p>
          
          <div className="space-y-2">
            {steps[currentStep - 1].details.map((detail, index) => (
              <div key={index} className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                <p className="text-sm text-blue-800 dark:text-blue-200 font-mono">
                  {detail}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between">
          <button
            onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
            disabled={currentStep === 1}
            className="px-4 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Previous
          </button>
          
          <span className="text-sm text-gray-600 dark:text-gray-300">
            Step {currentStep} of {steps.length}
          </span>
          
          <button
            onClick={() => setCurrentStep(Math.min(steps.length, currentStep + 1))}
            disabled={currentStep === steps.length}
            className="px-4 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Next
          </button>
        </div>

        {/* Quick Links */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h5 className="text-sm font-medium text-gray-800 dark:text-white mb-3">
            🔗 Quick Links
          </h5>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
            <a
              href="https://console.cloud.google.com/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 dark:text-blue-400 hover:underline"
            >
              → Google Cloud Console
            </a>
            <a
              href="https://console.cloud.google.com/apis/library/youtube.googleapis.com"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 dark:text-blue-400 hover:underline"
            >
              → YouTube Data API v3
            </a>
            <a
              href="https://console.cloud.google.com/apis/credentials"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 dark:text-blue-400 hover:underline"
            >
              → API Credentials
            </a>
            <a
              href="https://next-auth.js.org/providers/google"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 dark:text-blue-400 hover:underline"
            >
              → NextAuth Google Provider Docs
            </a>
          </div>
        </div>

        {/* Environment Variables Template */}
        {currentStep === 5 && (
          <div className="bg-gray-900 rounded-lg p-4">
            <h5 className="text-sm font-medium text-white mb-3">
              📝 .env.local Template
            </h5>
            <pre className="text-xs text-green-400 overflow-x-auto">
{`# Replace these with your actual values from Google Cloud Console
GOOGLE_CLIENT_ID=123456789-abcdefghijklmnop.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-your_actual_client_secret_here

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_secure_random_secret_key_here

# Optional: YouTube API Key for public data
YOUTUBE_API_KEY=your_youtube_api_key_here`}
            </pre>
          </div>
        )}

        {/* Completion Message */}
        {currentStep === steps.length && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <h5 className="text-sm font-medium text-green-800 dark:text-green-200">
                Setup Complete!
              </h5>
            </div>
            <p className="text-xs text-green-700 dark:text-green-300 mt-2">
              After updating your .env.local file and restarting the server, you'll be able to sign in with YouTube and access your purchased content.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
