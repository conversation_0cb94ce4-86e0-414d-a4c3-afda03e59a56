{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/AdvancedFormatSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface Format {\n  format_id: string;\n  ext: string;\n  quality: string;\n  filesize: number;\n  format_note: string;\n  vcodec: string;\n  acodec: string;\n  fps: number;\n  resolution: string;\n  tbr?: number;\n  vbr?: number;\n  abr?: number;\n  width?: number;\n  height?: number;\n}\n\ninterface AdvancedFormatSelectorProps {\n  formats: Format[];\n  onFormatSelect: (formatId: string) => void;\n  selectedFormat: string;\n}\n\nexport default function AdvancedFormatSelector({ \n  formats, \n  onFormatSelect, \n  selectedFormat \n}: AdvancedFormatSelectorProps) {\n  const [showAdvanced, setShowAdvanced] = useState(false);\n  const [sortBy, setSortBy] = useState<'quality' | 'size' | 'format'>('quality');\n  const [filterType, setFilterType] = useState<'all' | 'video' | 'audio'>('all');\n\n  const formatFileSize = (bytes: number) => {\n    if (!bytes) return 'Unknown';\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n  };\n\n  const getQualityScore = (format: Format) => {\n    if (format.height) return format.height;\n    if (format.resolution) {\n      const match = format.resolution.match(/(\\d+)x(\\d+)/);\n      return match ? parseInt(match[2]) : 0;\n    }\n    return 0;\n  };\n\n  const getFormatType = (format: Format) => {\n    const hasVideo = format.vcodec && format.vcodec !== 'none';\n    const hasAudio = format.acodec && format.acodec !== 'none';\n    \n    if (hasVideo && hasAudio) return 'video+audio';\n    if (hasVideo) return 'video';\n    if (hasAudio) return 'audio';\n    return 'unknown';\n  };\n\n  const getCodecInfo = (format: Format) => {\n    const video = format.vcodec && format.vcodec !== 'none' ? format.vcodec : null;\n    const audio = format.acodec && format.acodec !== 'none' ? format.acodec : null;\n    \n    if (video && audio) return `${video} + ${audio}`;\n    if (video) return video;\n    if (audio) return audio;\n    return 'Unknown';\n  };\n\n  const getBitrateInfo = (format: Format) => {\n    const parts = [];\n    if (format.vbr) parts.push(`V: ${format.vbr}k`);\n    if (format.abr) parts.push(`A: ${format.abr}k`);\n    if (format.tbr && !format.vbr && !format.abr) parts.push(`${format.tbr}k`);\n    return parts.length > 0 ? parts.join(', ') : 'Unknown';\n  };\n\n  const filteredFormats = formats.filter(format => {\n    if (filterType === 'all') return true;\n    const type = getFormatType(format);\n    if (filterType === 'video') return type === 'video+audio' || type === 'video';\n    if (filterType === 'audio') return type === 'audio';\n    return true;\n  });\n\n  const sortedFormats = [...filteredFormats].sort((a, b) => {\n    switch (sortBy) {\n      case 'quality':\n        return getQualityScore(b) - getQualityScore(a);\n      case 'size':\n        return (b.filesize || 0) - (a.filesize || 0);\n      case 'format':\n        return a.ext.localeCompare(b.ext);\n      default:\n        return 0;\n    }\n  });\n\n  if (!showAdvanced) {\n    return (\n      <div className=\"mt-2\">\n        <button\n          onClick={() => setShowAdvanced(true)}\n          className=\"text-sm text-blue-600 dark:text-blue-400 hover:underline\"\n        >\n          🔧 Advanced Format Selection ({formats.length} formats available)\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"mt-4 border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h4 className=\"text-sm font-medium text-gray-800 dark:text-white\">\n          🔧 Advanced Format Selection\n        </h4>\n        <button\n          onClick={() => setShowAdvanced(false)}\n          className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-200\"\n        >\n          ✕\n        </button>\n      </div>\n\n      {/* Controls */}\n      <div className=\"flex flex-wrap gap-4 mb-4\">\n        <div>\n          <label className=\"block text-xs text-gray-600 dark:text-gray-400 mb-1\">\n            Filter by Type\n          </label>\n          <select\n            value={filterType}\n            onChange={(e) => setFilterType(e.target.value as any)}\n            className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white\"\n          >\n            <option value=\"all\">All Formats</option>\n            <option value=\"video\">Video + Audio</option>\n            <option value=\"audio\">Audio Only</option>\n          </select>\n        </div>\n\n        <div>\n          <label className=\"block text-xs text-gray-600 dark:text-gray-400 mb-1\">\n            Sort by\n          </label>\n          <select\n            value={sortBy}\n            onChange={(e) => setSortBy(e.target.value as any)}\n            className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white\"\n          >\n            <option value=\"quality\">Quality</option>\n            <option value=\"size\">File Size</option>\n            <option value=\"format\">Format</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Format List */}\n      <div className=\"max-h-64 overflow-y-auto\">\n        <div className=\"space-y-2\">\n          {sortedFormats.slice(0, 20).map((format) => (\n            <label\n              key={format.format_id}\n              className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${\n                selectedFormat === format.format_id\n                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                  : 'border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'\n              }`}\n            >\n              <input\n                type=\"radio\"\n                name=\"advancedFormat\"\n                value={format.format_id}\n                checked={selectedFormat === format.format_id}\n                onChange={() => onFormatSelect(format.format_id)}\n                className=\"mr-3\"\n              />\n              \n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center justify-between mb-1\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"text-sm font-medium text-gray-800 dark:text-white\">\n                      {format.resolution || 'Audio Only'}\n                    </span>\n                    <span className=\"px-2 py-0.5 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded\">\n                      {format.ext.toUpperCase()}\n                    </span>\n                    <span className={`px-2 py-0.5 text-xs rounded ${\n                      getFormatType(format) === 'video+audio' \n                        ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200'\n                        : getFormatType(format) === 'video'\n                        ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200'\n                        : 'bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-200'\n                    }`}>\n                      {getFormatType(format) === 'video+audio' ? '🎥+🎵' : \n                       getFormatType(format) === 'video' ? '🎥' : '🎵'}\n                    </span>\n                  </div>\n                  <span className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    {formatFileSize(format.filesize)}\n                  </span>\n                </div>\n                \n                <div className=\"grid grid-cols-2 gap-4 text-xs text-gray-500 dark:text-gray-400\">\n                  <div>\n                    <span className=\"font-medium\">Codec:</span> {getCodecInfo(format)}\n                  </div>\n                  <div>\n                    <span className=\"font-medium\">Bitrate:</span> {getBitrateInfo(format)}\n                  </div>\n                  {format.fps && (\n                    <div>\n                      <span className=\"font-medium\">FPS:</span> {format.fps}\n                    </div>\n                  )}\n                  {format.format_note && (\n                    <div>\n                      <span className=\"font-medium\">Note:</span> {format.format_note}\n                    </div>\n                  )}\n                </div>\n              </div>\n            </label>\n          ))}\n        </div>\n      </div>\n\n      {sortedFormats.length > 20 && (\n        <div className=\"mt-2 text-xs text-gray-500 dark:text-gray-400 text-center\">\n          Showing top 20 of {sortedFormats.length} formats\n        </div>\n      )}\n\n      {sortedFormats.length === 0 && (\n        <div className=\"text-center py-4 text-gray-500 dark:text-gray-400\">\n          No formats match the current filter\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AA2Be,SAAS,uBAAuB,EAC7C,OAAO,EACP,cAAc,EACd,cAAc,EACc;;IAC5B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiC;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAExE,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,OAAO,OAAO;QACnB,MAAM,QAAQ;YAAC;YAAK;YAAM;YAAM;SAAK;QACrC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,CAAC,EAAE;IAC3E;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,OAAO,MAAM,EAAE,OAAO,OAAO,MAAM;QACvC,IAAI,OAAO,UAAU,EAAE;YACrB,MAAM,QAAQ,OAAO,UAAU,CAAC,KAAK,CAAC;YACtC,OAAO,QAAQ,SAAS,KAAK,CAAC,EAAE,IAAI;QACtC;QACA,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,WAAW,OAAO,MAAM,IAAI,OAAO,MAAM,KAAK;QACpD,MAAM,WAAW,OAAO,MAAM,IAAI,OAAO,MAAM,KAAK;QAEpD,IAAI,YAAY,UAAU,OAAO;QACjC,IAAI,UAAU,OAAO;QACrB,IAAI,UAAU,OAAO;QACrB,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ,OAAO,MAAM,IAAI,OAAO,MAAM,KAAK,SAAS,OAAO,MAAM,GAAG;QAC1E,MAAM,QAAQ,OAAO,MAAM,IAAI,OAAO,MAAM,KAAK,SAAS,OAAO,MAAM,GAAG;QAE1E,IAAI,SAAS,OAAO,OAAO,GAAG,MAAM,GAAG,EAAE,OAAO;QAChD,IAAI,OAAO,OAAO;QAClB,IAAI,OAAO,OAAO;QAClB,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,QAAQ,EAAE;QAChB,IAAI,OAAO,GAAG,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;QAC9C,IAAI,OAAO,GAAG,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;QAC9C,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,EAAE,MAAM,IAAI,CAAC,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC;QACzE,OAAO,MAAM,MAAM,GAAG,IAAI,MAAM,IAAI,CAAC,QAAQ;IAC/C;IAEA,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA;QACrC,IAAI,eAAe,OAAO,OAAO;QACjC,MAAM,OAAO,cAAc;QAC3B,IAAI,eAAe,SAAS,OAAO,SAAS,iBAAiB,SAAS;QACtE,IAAI,eAAe,SAAS,OAAO,SAAS;QAC5C,OAAO;IACT;IAEA,MAAM,gBAAgB;WAAI;KAAgB,CAAC,IAAI,CAAC,CAAC,GAAG;QAClD,OAAQ;YACN,KAAK;gBACH,OAAO,gBAAgB,KAAK,gBAAgB;YAC9C,KAAK;gBACH,OAAO,CAAC,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,IAAI,CAAC;YAC7C,KAAK;gBACH,OAAO,EAAE,GAAG,CAAC,aAAa,CAAC,EAAE,GAAG;YAClC;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,cAAc;QACjB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBACC,SAAS,IAAM,gBAAgB;gBAC/B,WAAU;;oBACX;oBACgC,QAAQ,MAAM;oBAAC;;;;;;;;;;;;IAItD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoD;;;;;;kCAGlE,6LAAC;wBACC,SAAS,IAAM,gBAAgB;wBAC/B,WAAU;kCACX;;;;;;;;;;;;0BAMH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAsD;;;;;;0CAGvE,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;;;;;;;;;;;;;kCAI1B,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAsD;;;;;;0CAGvE,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gCACzC,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;0BAM7B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,cAAc,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,uBAC/B,6LAAC;4BAEC,WAAW,CAAC,yEAAyE,EACnF,mBAAmB,OAAO,SAAS,GAC/B,mDACA,gFACJ;;8CAEF,6LAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,OAAO,SAAS;oCACvB,SAAS,mBAAmB,OAAO,SAAS;oCAC5C,UAAU,IAAM,eAAe,OAAO,SAAS;oCAC/C,WAAU;;;;;;8CAGZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,OAAO,UAAU,IAAI;;;;;;sEAExB,6LAAC;4DAAK,WAAU;sEACb,OAAO,GAAG,CAAC,WAAW;;;;;;sEAEzB,6LAAC;4DAAK,WAAW,CAAC,4BAA4B,EAC5C,cAAc,YAAY,gBACtB,yEACA,cAAc,YAAY,UAC1B,qEACA,4EACJ;sEACC,cAAc,YAAY,gBAAgB,UAC1C,cAAc,YAAY,UAAU,OAAO;;;;;;;;;;;;8DAGhD,6LAAC;oDAAK,WAAU;8DACb,eAAe,OAAO,QAAQ;;;;;;;;;;;;sDAInC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAa;wDAAE,aAAa;;;;;;;8DAE5D,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAe;wDAAE,eAAe;;;;;;;gDAE/D,OAAO,GAAG,kBACT,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAW;wDAAE,OAAO,GAAG;;;;;;;gDAGxD,OAAO,WAAW,kBACjB,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAY;wDAAE,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;2BAvDjE,OAAO,SAAS;;;;;;;;;;;;;;;YAiE5B,cAAc,MAAM,GAAG,oBACtB,6LAAC;gBAAI,WAAU;;oBAA4D;oBACtD,cAAc,MAAM;oBAAC;;;;;;;YAI3C,cAAc,MAAM,KAAK,mBACxB,6LAAC;gBAAI,WAAU;0BAAoD;;;;;;;;;;;;AAM3E;GAzNwB;KAAA", "debugId": null}}, {"offset": {"line": 461, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/VideoInfoCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Image from 'next/image';\nimport AdvancedFormatSelector from './AdvancedFormatSelector';\n\ninterface VideoInfo {\n  title: string;\n  description: string;\n  duration: number;\n  uploader: string;\n  upload_date: string;\n  view_count: number;\n  thumbnail: string;\n  formats: Array<{\n    format_id: string;\n    ext: string;\n    quality: string;\n    filesize: number;\n    format_note: string;\n    vcodec: string;\n    acodec: string;\n    fps: number;\n    resolution: string;\n  }>;\n  url: string;\n  webpage_url: string;\n  extractor: string;\n}\n\ninterface VideoInfoCardProps {\n  videoInfo: VideoInfo;\n  onDownload: (format?: string, quality?: string, type?: string, audioFormat?: string, subtitleOptions?: any) => void;\n}\n\nexport default function VideoInfoCard({ videoInfo, onDownload }: VideoInfoCardProps) {\n  const [selectedFormat, setSelectedFormat] = useState('best');\n  const [selectedQuality, setSelectedQuality] = useState('');\n  const [downloadType, setDownloadType] = useState<'video' | 'audio'>('video');\n  const [audioFormat, setAudioFormat] = useState('mp3');\n  const [includeSubtitles, setIncludeSubtitles] = useState(false);\n  const [subtitleLanguages, setSubtitleLanguages] = useState('en');\n\n  const formatDuration = (seconds: number) => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const formatDate = (dateString: string) => {\n    if (!dateString) return 'Unknown';\n    const year = dateString.substring(0, 4);\n    const month = dateString.substring(4, 6);\n    const day = dateString.substring(6, 8);\n    return `${year}-${month}-${day}`;\n  };\n\n  const formatViewCount = (count: number) => {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  };\n\n  const handleDownload = () => {\n    const subtitleOptions = includeSubtitles ? {\n      includeSubtitles: true,\n      subtitleLanguages: subtitleLanguages\n    } : undefined;\n\n    if (downloadType === 'audio') {\n      onDownload(`bestaudio[ext=${audioFormat}]/bestaudio`, undefined, 'audio', audioFormat, subtitleOptions);\n    } else if (selectedFormat === 'best') {\n      onDownload(undefined, undefined, undefined, undefined, subtitleOptions);\n    } else if (selectedFormat === 'quality' && selectedQuality) {\n      onDownload(undefined, selectedQuality, undefined, undefined, subtitleOptions);\n    } else {\n      onDownload(selectedFormat, undefined, undefined, undefined, subtitleOptions);\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden\">\n      <div className=\"md:flex\">\n        <div className=\"md:w-1/3\">\n          {videoInfo.thumbnail && (\n            <div className=\"relative aspect-video\">\n              <Image\n                src={videoInfo.thumbnail}\n                alt={videoInfo.title}\n                fill\n                className=\"object-cover\"\n                unoptimized\n              />\n            </div>\n          )}\n        </div>\n\n        <div className=\"md:w-2/3 p-6\">\n          <h2 className=\"text-xl font-bold text-gray-800 dark:text-white mb-2\">\n            {videoInfo.title}\n          </h2>\n          \n          <div className=\"grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-300 mb-4\">\n            <div>\n              <span className=\"font-medium\">Uploader:</span> {videoInfo.uploader}\n            </div>\n            <div>\n              <span className=\"font-medium\">Duration:</span> {formatDuration(videoInfo.duration)}\n            </div>\n            <div>\n              <span className=\"font-medium\">Views:</span> {formatViewCount(videoInfo.view_count)}\n            </div>\n            <div>\n              <span className=\"font-medium\">Upload Date:</span> {formatDate(videoInfo.upload_date)}\n            </div>\n            <div>\n              <span className=\"font-medium\">Source:</span> {videoInfo.extractor}\n            </div>\n          </div>\n\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Download Type\n              </label>\n              <div className=\"flex space-x-4 mb-4\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    name=\"downloadType\"\n                    value=\"video\"\n                    checked={downloadType === 'video'}\n                    onChange={(e) => setDownloadType(e.target.value as 'video' | 'audio')}\n                    className=\"mr-2\"\n                  />\n                  <span className=\"text-sm\">🎥 Video</span>\n                </label>\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    name=\"downloadType\"\n                    value=\"audio\"\n                    checked={downloadType === 'audio'}\n                    onChange={(e) => setDownloadType(e.target.value as 'video' | 'audio')}\n                    className=\"mr-2\"\n                  />\n                  <span className=\"text-sm\">🎵 Audio Only</span>\n                </label>\n              </div>\n            </div>\n\n            {downloadType === 'audio' && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Audio Format\n                </label>\n                <select\n                  value={audioFormat}\n                  onChange={(e) => setAudioFormat(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white\"\n                >\n                  <option value=\"mp3\">MP3 (Most Compatible)</option>\n                  <option value=\"m4a\">M4A (High Quality)</option>\n                  <option value=\"wav\">WAV (Uncompressed)</option>\n                  <option value=\"flac\">FLAC (Lossless)</option>\n                  <option value=\"ogg\">OGG (Open Source)</option>\n                </select>\n              </div>\n            )}\n\n            <div>\n              <label className=\"flex items-center mb-2\">\n                <input\n                  type=\"checkbox\"\n                  checked={includeSubtitles}\n                  onChange={(e) => setIncludeSubtitles(e.target.checked)}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  📝 Download Subtitles/Captions\n                </span>\n              </label>\n              \n              {includeSubtitles && (\n                <div className=\"ml-6\">\n                  <input\n                    type=\"text\"\n                    value={subtitleLanguages}\n                    onChange={(e) => setSubtitleLanguages(e.target.value)}\n                    placeholder=\"en,es,fr\"\n                    className=\"w-full px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white\"\n                  />\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                    Language codes (comma-separated): en, es, fr, de, ja\n                  </p>\n                </div>\n              )}\n            </div>\n\n            {downloadType === 'video' && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Video Quality Options\n                </label>\n                \n                <div className=\"space-y-2\">\n                  <label className=\"flex items-center\">\n                    <input\n                      type=\"radio\"\n                      name=\"format\"\n                      value=\"best\"\n                      checked={selectedFormat === 'best'}\n                      onChange={(e) => setSelectedFormat(e.target.value)}\n                      className=\"mr-2\"\n                    />\n                    <span className=\"text-sm\">Best Quality (Automatic)</span>\n                  </label>\n\n                  <label className=\"flex items-center\">\n                    <input\n                      type=\"radio\"\n                      name=\"format\"\n                      value=\"quality\"\n                      checked={selectedFormat === 'quality'}\n                      onChange={(e) => setSelectedFormat(e.target.value)}\n                      className=\"mr-2\"\n                    />\n                    <span className=\"text-sm\">Select Quality:</span>\n                    <select\n                      value={selectedQuality}\n                      onChange={(e) => {\n                        setSelectedQuality(e.target.value);\n                        setSelectedFormat('quality');\n                      }}\n                      className=\"ml-2 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm dark:bg-gray-700 dark:text-white\"\n                      disabled={selectedFormat !== 'quality'}\n                    >\n                      <option value=\"\">Select...</option>\n                      <option value=\"720\">720p</option>\n                      <option value=\"480\">480p</option>\n                      <option value=\"360\">360p</option>\n                      <option value=\"240\">240p</option>\n                    </select>\n                  </label>\n                </div>\n\n                <AdvancedFormatSelector\n                  formats={videoInfo.formats}\n                  onFormatSelect={setSelectedFormat}\n                  selectedFormat={selectedFormat}\n                />\n              </div>\n            )}\n\n            <button\n              onClick={handleDownload}\n              className=\"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors\"\n            >\n              {downloadType === 'audio' ? `Extract Audio (${audioFormat.toUpperCase()})` : 'Download Video'}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAmCe,SAAS,cAAc,EAAE,SAAS,EAAE,UAAU,EAAsB;;IACjF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,iBAAiB,CAAC;QACtB,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;QAC9C,MAAM,OAAO,UAAU;QAEvB,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;QAC9F;QACA,OAAO,GAAG,QAAQ,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACzD;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,MAAM,OAAO,WAAW,SAAS,CAAC,GAAG;QACrC,MAAM,QAAQ,WAAW,SAAS,CAAC,GAAG;QACtC,MAAM,MAAM,WAAW,SAAS,CAAC,GAAG;QACpC,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK;IAClC;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,SAAS,SAAS;YACpB,OAAO,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,KAAK;QACxC,OAAO,IAAI,SAAS,MAAM;YACxB,OAAO,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,KAAK;QACrC;QACA,OAAO,MAAM,QAAQ;IACvB;IAEA,MAAM,iBAAiB;QACrB,MAAM,kBAAkB,mBAAmB;YACzC,kBAAkB;YAClB,mBAAmB;QACrB,IAAI;QAEJ,IAAI,iBAAiB,SAAS;YAC5B,WAAW,CAAC,cAAc,EAAE,YAAY,WAAW,CAAC,EAAE,WAAW,SAAS,aAAa;QACzF,OAAO,IAAI,mBAAmB,QAAQ;YACpC,WAAW,WAAW,WAAW,WAAW,WAAW;QACzD,OAAO,IAAI,mBAAmB,aAAa,iBAAiB;YAC1D,WAAW,WAAW,iBAAiB,WAAW,WAAW;QAC/D,OAAO;YACL,WAAW,gBAAgB,WAAW,WAAW,WAAW;QAC9D;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACZ,UAAU,SAAS,kBAClB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,UAAU,SAAS;4BACxB,KAAK,UAAU,KAAK;4BACpB,IAAI;4BACJ,WAAU;4BACV,WAAW;;;;;;;;;;;;;;;;8BAMnB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,UAAU,KAAK;;;;;;sCAGlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAgB;wCAAE,UAAU,QAAQ;;;;;;;8CAEpE,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAgB;wCAAE,eAAe,UAAU,QAAQ;;;;;;;8CAEnF,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAa;wCAAE,gBAAgB,UAAU,UAAU;;;;;;;8CAEnF,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAmB;wCAAE,WAAW,UAAU,WAAW;;;;;;;8CAErF,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAc;wCAAE,UAAU,SAAS;;;;;;;;;;;;;sCAIrE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,iBAAiB;4DAC1B,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAC/C,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,iBAAiB;4DAC1B,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAC/C,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;gCAK/B,iBAAiB,yBAChB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;;;;;;;;;;;;;8CAK1B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,OAAO;oDACrD,WAAU;;;;;;8DAEZ,6LAAC;oDAAK,WAAU;8DAAuD;;;;;;;;;;;;wCAKxE,kCACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDACpD,aAAY;oDACZ,WAAU;;;;;;8DAEZ,6LAAC;oDAAE,WAAU;8DAAgD;;;;;;;;;;;;;;;;;;gCAOlE,iBAAiB,yBAChB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAInF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,mBAAmB;4DAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4DACjD,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAG5B,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,mBAAmB;4DAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4DACjD,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC;4DACC,OAAO;4DACP,UAAU,CAAC;gEACT,mBAAmB,EAAE,MAAM,CAAC,KAAK;gEACjC,kBAAkB;4DACpB;4DACA,WAAU;4DACV,UAAU,mBAAmB;;8EAE7B,6LAAC;oEAAO,OAAM;8EAAG;;;;;;8EACjB,6LAAC;oEAAO,OAAM;8EAAM;;;;;;8EACpB,6LAAC;oEAAO,OAAM;8EAAM;;;;;;8EACpB,6LAAC;oEAAO,OAAM;8EAAM;;;;;;8EACpB,6LAAC;oEAAO,OAAM;8EAAM;;;;;;;;;;;;;;;;;;;;;;;;sDAK1B,6LAAC,+IAAA,CAAA,UAAsB;4CACrB,SAAS,UAAU,OAAO;4CAC1B,gBAAgB;4CAChB,gBAAgB;;;;;;;;;;;;8CAKtB,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAET,iBAAiB,UAAU,CAAC,eAAe,EAAE,YAAY,WAAW,GAAG,CAAC,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3F;GA9OwB;KAAA", "debugId": null}}, {"offset": {"line": 1070, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/DownloadProgress.tsx"], "sourcesContent": ["'use client';\n\ninterface DownloadStatus {\n  id: string;\n  status: 'downloading' | 'completed' | 'failed';\n  progress: number;\n  filename: string | null;\n  url: string;\n  startTime: number;\n  error: string | null;\n}\n\ninterface DownloadProgressProps {\n  download: DownloadStatus;\n}\n\nexport default function DownloadProgress({ download }: DownloadProgressProps) {\n  const getStatusColor = () => {\n    switch (download.status) {\n      case 'downloading':\n        return 'bg-blue-600';\n      case 'completed':\n        return 'bg-green-600';\n      case 'failed':\n        return 'bg-red-600';\n      default:\n        return 'bg-gray-600';\n    }\n  };\n\n  const getStatusText = () => {\n    switch (download.status) {\n      case 'downloading':\n        return 'Downloading...';\n      case 'completed':\n        return 'Completed';\n      case 'failed':\n        return 'Failed';\n      default:\n        return 'Unknown';\n    }\n  };\n\n  const formatElapsedTime = () => {\n    const elapsed = Date.now() - download.startTime;\n    const seconds = Math.floor(elapsed / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const hours = Math.floor(minutes / 60);\n\n    if (hours > 0) {\n      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;\n    } else if (minutes > 0) {\n      return `${minutes}m ${seconds % 60}s`;\n    } else {\n      return `${seconds}s`;\n    }\n  };\n\n  const getDisplayUrl = () => {\n    try {\n      const url = new URL(download.url);\n      return url.hostname;\n    } catch {\n      return download.url.substring(0, 50) + '...';\n    }\n  };\n\n  return (\n    <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n      <div className=\"flex items-center justify-between mb-2\">\n        <div className=\"flex-1 min-w-0\">\n          <p className=\"text-sm font-medium text-gray-800 dark:text-white truncate\">\n            {download.filename || 'Preparing download...'}\n          </p>\n          <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\n            {getDisplayUrl()}\n          </p>\n        </div>\n        <div className=\"ml-4 flex items-center space-x-2\">\n          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white ${getStatusColor()}`}>\n            {getStatusText()}\n          </span>\n          <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n            {formatElapsedTime()}\n          </span>\n        </div>\n      </div>\n\n      {/* Progress Bar */}\n      <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2\">\n        <div\n          className={`h-2 rounded-full transition-all duration-300 ${getStatusColor()}`}\n          style={{ width: `${Math.max(0, Math.min(100, download.progress))}%` }}\n        ></div>\n      </div>\n\n      <div className=\"flex justify-between items-center\">\n        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n          {download.progress.toFixed(1)}%\n        </span>\n        {download.status === 'completed' && download.filename && (\n          <a\n            href={`/downloads/${download.filename}`}\n            download\n            className=\"text-xs text-blue-600 dark:text-blue-400 hover:underline\"\n          >\n            Download File\n          </a>\n        )}\n      </div>\n\n      {download.error && (\n        <div className=\"mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded\">\n          <p className=\"text-xs text-red-800 dark:text-red-200\">\n            Error: {download.error}\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAgBe,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;IAC1E,MAAM,iBAAiB;QACrB,OAAQ,SAAS,MAAM;YACrB,KAAK;gBACH,OAAO;YACT,KAAK;gBA<PERSON>,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ,SAAS,MAAM;YACrB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,UAAU,KAAK,GAAG,KAAK,SAAS,SAAS;QAC/C,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QAEnC,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,EAAE,EAAE,UAAU,GAAG,EAAE,EAAE,UAAU,GAAG,CAAC,CAAC;QACtD,OAAO,IAAI,UAAU,GAAG;YACtB,OAAO,GAAG,QAAQ,EAAE,EAAE,UAAU,GAAG,CAAC,CAAC;QACvC,OAAO;YACL,OAAO,GAAG,QAAQ,CAAC,CAAC;QACtB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,MAAM,IAAI,IAAI,SAAS,GAAG;YAChC,OAAO,IAAI,QAAQ;QACrB,EAAE,OAAM;YACN,OAAO,SAAS,GAAG,CAAC,SAAS,CAAC,GAAG,MAAM;QACzC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CACV,SAAS,QAAQ,IAAI;;;;;;0CAExB,6LAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;kCAGL,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAW,CAAC,mFAAmF,EAAE,kBAAkB;0CACtH;;;;;;0CAEH,6LAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;;;;;;;0BAMP,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAW,CAAC,6CAA6C,EAAE,kBAAkB;oBAC7E,OAAO;wBAAE,OAAO,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,SAAS,QAAQ,GAAG,CAAC,CAAC;oBAAC;;;;;;;;;;;0BAIxE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;;4BACb,SAAS,QAAQ,CAAC,OAAO,CAAC;4BAAG;;;;;;;oBAE/B,SAAS,MAAM,KAAK,eAAe,SAAS,QAAQ,kBACnD,6LAAC;wBACC,MAAM,CAAC,WAAW,EAAE,SAAS,QAAQ,EAAE;wBACvC,QAAQ;wBACR,WAAU;kCACX;;;;;;;;;;;;YAMJ,SAAS,KAAK,kBACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;wBAAyC;wBAC5C,SAAS,KAAK;;;;;;;;;;;;;;;;;;AAMlC;KAxGwB", "debugId": null}}, {"offset": {"line": 1268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/DownloadHistory.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface DownloadFile {\n  filename: string;\n  size: number;\n  downloadDate: string;\n  downloadUrl: string;\n}\n\ninterface DownloadHistoryProps {\n  downloads: DownloadFile[];\n  onDelete: (filename: string) => void;\n  onRefresh: () => void;\n}\n\nexport default function DownloadHistory({ downloads, onDelete, onRefresh }: DownloadHistoryProps) {\n  const [deletingFiles, setDeletingFiles] = useState<Set<string>>(new Set());\n\n  const formatFileSize = (bytes: number) => {\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n  };\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();\n  };\n\n  const handleDelete = async (filename: string) => {\n    if (confirm(`Are you sure you want to delete \"${filename}\"?`)) {\n      setDeletingFiles(prev => new Set(prev).add(filename));\n      try {\n        await onDelete(filename);\n      } finally {\n        setDeletingFiles(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(filename);\n          return newSet;\n        });\n      }\n    }\n  };\n\n  const getFileExtension = (filename: string) => {\n    return filename.split('.').pop()?.toLowerCase() || '';\n  };\n\n  const getFileIcon = (filename: string) => {\n    const ext = getFileExtension(filename);\n    switch (ext) {\n      case 'mp4':\n      case 'avi':\n      case 'mov':\n      case 'mkv':\n      case 'webm':\n        return '🎥';\n      case 'mp3':\n      case 'wav':\n      case 'flac':\n      case 'm4a':\n        return '🎵';\n      default:\n        return '📄';\n    }\n  };\n\n  if (downloads.length === 0) {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white\">\n            Download History\n          </h3>\n          <button\n            onClick={onRefresh}\n            className=\"px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n          >\n            Refresh\n          </button>\n        </div>\n        <div className=\"text-center py-8\">\n          <p className=\"text-gray-500 dark:text-gray-400\">No downloads yet</p>\n          <p className=\"text-sm text-gray-400 dark:text-gray-500 mt-1\">\n            Downloaded files will appear here\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white\">\n          Download History ({downloads.length})\n        </h3>\n        <button\n          onClick={onRefresh}\n          className=\"px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n        >\n          Refresh\n        </button>\n      </div>\n\n      <div className=\"space-y-3\">\n        {downloads.map((download) => (\n          <div\n            key={download.filename}\n            className=\"flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <div className=\"flex items-center space-x-3 flex-1 min-w-0\">\n              <span className=\"text-2xl\">{getFileIcon(download.filename)}</span>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-800 dark:text-white truncate\">\n                  {download.filename}\n                </p>\n                <div className=\"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400\">\n                  <span>{formatFileSize(download.size)}</span>\n                  <span>{formatDate(download.downloadDate)}</span>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-2 ml-4\">\n              <a\n                href={download.downloadUrl}\n                download\n                className=\"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\"\n              >\n                Download\n              </a>\n              <button\n                onClick={() => handleDelete(download.filename)}\n                disabled={deletingFiles.has(download.filename)}\n                className=\"px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                {deletingFiles.has(download.filename) ? 'Deleting...' : 'Delete'}\n              </button>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {downloads.length > 5 && (\n        <div className=\"mt-4 text-center\">\n          <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n            Showing {downloads.length} downloads\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAiBe,SAAS,gBAAgB,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAwB;;IAC9F,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAEpE,MAAM,iBAAiB,CAAC;QACtB,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,CAAC,EAAE;IAC3E;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,KAAK,MAAM,KAAK,kBAAkB;IAClE;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,QAAQ,CAAC,iCAAiC,EAAE,SAAS,EAAE,CAAC,GAAG;YAC7D,iBAAiB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC;YAC3C,IAAI;gBACF,MAAM,SAAS;YACjB,SAAU;gBACR,iBAAiB,CAAA;oBACf,MAAM,SAAS,IAAI,IAAI;oBACvB,OAAO,MAAM,CAAC;oBACd,OAAO;gBACT;YACF;QACF;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI,iBAAiB;IACrD;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,MAAM,iBAAiB;QAC7B,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAIH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;sCAChD,6LAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;;;;;;;IAMrE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAAsD;4BAC/C,UAAU,MAAM;4BAAC;;;;;;;kCAEtC,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAKH,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC;wBAEC,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAY,YAAY,SAAS,QAAQ;;;;;;kDACzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DACV,SAAS,QAAQ;;;;;;0DAEpB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAM,eAAe,SAAS,IAAI;;;;;;kEACnC,6LAAC;kEAAM,WAAW,SAAS,YAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAK7C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAM,SAAS,WAAW;wCAC1B,QAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,aAAa,SAAS,QAAQ;wCAC7C,UAAU,cAAc,GAAG,CAAC,SAAS,QAAQ;wCAC7C,WAAU;kDAET,cAAc,GAAG,CAAC,SAAS,QAAQ,IAAI,gBAAgB;;;;;;;;;;;;;uBA7BvD,SAAS,QAAQ;;;;;;;;;;YAoC3B,UAAU,MAAM,GAAG,mBAClB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;wBAA2C;wBAC7C,UAAU,MAAM;wBAAC;;;;;;;;;;;;;;;;;;AAMtC;GA1IwB;KAAA", "debugId": null}}, {"offset": {"line": 1565, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/BatchDownloader.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface BatchDownloadProps {\n  onBatchDownload: (urls: string[], options: BatchDownloadOptions) => void;\n}\n\ninterface BatchDownloadOptions {\n  type: 'video' | 'audio';\n  audioFormat?: string;\n  quality?: string;\n  format?: string;\n}\n\nexport default function BatchDownloader({ onBatchDownload }: BatchDownloadProps) {\n  const [urlText, setUrlText] = useState('');\n  const [downloadType, setDownloadType] = useState<'video' | 'audio'>('video');\n  const [audioFormat, setAudioFormat] = useState('mp3');\n  const [quality, setQuality] = useState('');\n  const [isExpanded, setIsExpanded] = useState(false);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    // Parse URLs from text (one per line)\n    const urls = urlText\n      .split('\\n')\n      .map(url => url.trim())\n      .filter(url => url.length > 0 && isValidUrl(url));\n\n    if (urls.length === 0) {\n      alert('Please enter at least one valid URL');\n      return;\n    }\n\n    const options: BatchDownloadOptions = {\n      type: downloadType,\n      audioFormat: downloadType === 'audio' ? audioFormat : undefined,\n      quality: downloadType === 'video' ? quality : undefined,\n    };\n\n    onBatchDownload(urls, options);\n    setUrlText(''); // Clear the text area after submission\n  };\n\n  const isValidUrl = (string: string) => {\n    try {\n      new URL(string);\n      return true;\n    } catch {\n      return false;\n    }\n  };\n\n  const getUrlCount = () => {\n    return urlText\n      .split('\\n')\n      .map(url => url.trim())\n      .filter(url => url.length > 0 && isValidUrl(url)).length;\n  };\n\n  if (!isExpanded) {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8\">\n        <button\n          onClick={() => setIsExpanded(true)}\n          className=\"w-full flex items-center justify-between text-left\"\n        >\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white\">\n              📦 Batch Download\n            </h3>\n            <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n              Download multiple videos or playlists at once\n            </p>\n          </div>\n          <svg className=\"w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n          </svg>\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white\">\n            📦 Batch Download\n          </h3>\n          <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n            Enter multiple URLs (one per line) or playlist URLs\n          </p>\n        </div>\n        <button\n          onClick={() => setIsExpanded(false)}\n          className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-200\"\n        >\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 15l7-7 7 7\" />\n          </svg>\n        </button>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-4\">\n        {/* URL Input */}\n        <div>\n          <label htmlFor=\"batch-urls\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            URLs or Playlist Links\n          </label>\n          <textarea\n            id=\"batch-urls\"\n            value={urlText}\n            onChange={(e) => setUrlText(e.target.value)}\n            placeholder=\"https://www.youtube.com/watch?v=example1&#10;https://www.youtube.com/watch?v=example2&#10;https://www.youtube.com/playlist?list=example\"\n            rows={6}\n            className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-vertical\"\n          />\n          <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n            Valid URLs detected: {getUrlCount()}\n          </p>\n        </div>\n\n        {/* Download Type */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Download Type\n            </label>\n            <div className=\"flex space-x-4\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  name=\"batchDownloadType\"\n                  value=\"video\"\n                  checked={downloadType === 'video'}\n                  onChange={(e) => setDownloadType(e.target.value as 'video' | 'audio')}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm\">🎥 Video</span>\n              </label>\n              <label className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  name=\"batchDownloadType\"\n                  value=\"audio\"\n                  checked={downloadType === 'audio'}\n                  onChange={(e) => setDownloadType(e.target.value as 'video' | 'audio')}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm\">🎵 Audio Only</span>\n              </label>\n            </div>\n          </div>\n\n          {/* Format/Quality Selection */}\n          <div>\n            {downloadType === 'audio' ? (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Audio Format\n                </label>\n                <select\n                  value={audioFormat}\n                  onChange={(e) => setAudioFormat(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white\"\n                >\n                  <option value=\"mp3\">MP3</option>\n                  <option value=\"m4a\">M4A</option>\n                  <option value=\"wav\">WAV</option>\n                  <option value=\"flac\">FLAC</option>\n                  <option value=\"ogg\">OGG</option>\n                </select>\n              </div>\n            ) : (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Video Quality\n                </label>\n                <select\n                  value={quality}\n                  onChange={(e) => setQuality(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white\"\n                >\n                  <option value=\"\">Best Available</option>\n                  <option value=\"720\">720p</option>\n                  <option value=\"480\">480p</option>\n                  <option value=\"360\">360p</option>\n                  <option value=\"240\">240p</option>\n                </select>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Submit Button */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"text-sm text-gray-600 dark:text-gray-300\">\n            {getUrlCount() > 0 && (\n              <span>Ready to download {getUrlCount()} item{getUrlCount() !== 1 ? 's' : ''}</span>\n            )}\n          </div>\n          <button\n            type=\"submit\"\n            disabled={getUrlCount() === 0}\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            Start Batch Download\n          </button>\n        </div>\n      </form>\n\n      {/* Tips */}\n      <div className=\"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg\">\n        <h4 className=\"text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">💡 Tips:</h4>\n        <ul className=\"text-xs text-blue-700 dark:text-blue-300 space-y-1\">\n          <li>• Playlist URLs will automatically download all videos in the playlist</li>\n          <li>• Each URL should be on a separate line</li>\n          <li>• Invalid URLs will be automatically filtered out</li>\n          <li>• Large batches may take significant time to complete</li>\n        </ul>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAee,SAAS,gBAAgB,EAAE,eAAe,EAAsB;;IAC7E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,sCAAsC;QACtC,MAAM,OAAO,QACV,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IACnB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,GAAG,KAAK,WAAW;QAE9C,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,MAAM;YACN;QACF;QAEA,MAAM,UAAgC;YACpC,MAAM;YACN,aAAa,iBAAiB,UAAU,cAAc;YACtD,SAAS,iBAAiB,UAAU,UAAU;QAChD;QAEA,gBAAgB,MAAM;QACtB,WAAW,KAAK,uCAAuC;IACzD;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI;YACF,IAAI,IAAI;YACR,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,cAAc;QAClB,OAAO,QACJ,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IACnB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,GAAG,KAAK,WAAW,MAAM,MAAM;IAC5D;IAEA,IAAI,CAAC,YAAY;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBACC,SAAS,IAAM,cAAc;gBAC7B,WAAU;;kCAEV,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAI1D,6LAAC;wBAAI,WAAU;wBAAwB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC/E,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;IAK/E;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAI1D,6LAAC;wBACC,SAAS,IAAM,cAAc;wBAC7B,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAK3E,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAa,WAAU;0CAAkE;;;;;;0CAGxG,6LAAC;gCACC,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC1C,aAAY;gCACZ,MAAM;gCACN,WAAU;;;;;;0CAEZ,6LAAC;gCAAE,WAAU;;oCAAgD;oCACrC;;;;;;;;;;;;;kCAK1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAM;wDACN,SAAS,iBAAiB;wDAC1B,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDAC/C,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAE5B,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAM;wDACN,SAAS,iBAAiB;wDAC1B,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDAC/C,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAMhC,6LAAC;0CACE,iBAAiB,wBAChB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;;;;;;;;;;;;yDAIxB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC1C,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,mBACf,6LAAC;;wCAAK;wCAAmB;wCAAc;wCAAM,kBAAkB,IAAI,MAAM;;;;;;;;;;;;0CAG7E,6LAAC;gCACC,MAAK;gCACL,UAAU,kBAAkB;gCAC5B,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAC1E,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAKd;GAnNwB;KAAA", "debugId": null}}, {"offset": {"line": 2128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/VideoDownloader.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport VideoInfoCard from './VideoInfoCard';\nimport DownloadProgress from './DownloadProgress';\nimport DownloadHistory from './DownloadHistory';\nimport BatchDownloader from './BatchDownloader';\nimport SettingsPanel from './SettingsPanel';\nimport DownloadQueue from './DownloadQueue';\n\ninterface VideoInfo {\n  title: string;\n  description: string;\n  duration: number;\n  uploader: string;\n  upload_date: string;\n  view_count: number;\n  thumbnail: string;\n  formats: Array<{\n    format_id: string;\n    ext: string;\n    quality: string;\n    filesize: number;\n    format_note: string;\n    vcodec: string;\n    acodec: string;\n    fps: number;\n    resolution: string;\n  }>;\n  url: string;\n  webpage_url: string;\n  extractor: string;\n}\n\ninterface DownloadStatus {\n  id: string;\n  status: 'downloading' | 'completed' | 'failed';\n  progress: number;\n  filename: string | null;\n  url: string;\n  startTime: number;\n  error: string | null;\n}\n\nexport default function VideoDownloader() {\n  const [url, setUrl] = useState('');\n  const [videoInfo, setVideoInfo] = useState<VideoInfo | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [activeDownloads, setActiveDownloads] = useState<DownloadStatus[]>([]);\n  const [downloadHistory, setDownloadHistory] = useState<any[]>([]);\n  const [activeBatchDownloads, setActiveBatchDownloads] = useState<any[]>([]);\n  const [showSettings, setShowSettings] = useState(false);\n  const [downloadQueue, setDownloadQueue] = useState<any[]>([]);\n  const [settings, setSettings] = useState<any>({\n    maxConcurrentDownloads: 3,\n    defaultQuality: 'best',\n    defaultAudioFormat: 'mp3',\n    autoSubtitles: false,\n    defaultSubtitleLanguages: 'en'\n  });\n\n  const fetchVideoInfo = async () => {\n    if (!url.trim()) {\n      setError('Please enter a valid URL');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    setVideoInfo(null);\n\n    try {\n      const response = await fetch('/api/video-info', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ url: url.trim() }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to fetch video information');\n      }\n\n      if (data.success) {\n        setVideoInfo(data.data);\n      } else {\n        throw new Error(data.error || 'Unknown error occurred');\n      }\n    } catch (err: any) {\n      setError(err.message || 'Failed to fetch video information');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const startDownload = async (format?: string, quality?: string, type?: string, audioFormat?: string, subtitleOptions?: any) => {\n    if (!videoInfo) return;\n\n    try {\n      const response = await fetch('/api/download', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          url: videoInfo.url,\n          format,\n          quality,\n          type,\n          audioFormat,\n          subtitleOptions,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to start download');\n      }\n\n      if (data.success) {\n        // Add to active downloads\n        const newDownload: DownloadStatus = {\n          id: data.downloadId,\n          status: 'downloading',\n          progress: 0,\n          filename: null,\n          url: videoInfo.url,\n          startTime: Date.now(),\n          error: null,\n        };\n\n        setActiveDownloads(prev => [...prev, newDownload]);\n        \n        // Start polling for progress\n        pollDownloadProgress(data.downloadId);\n      }\n    } catch (err: any) {\n      setError(err.message || 'Failed to start download');\n    }\n  };\n\n  const pollDownloadProgress = async (downloadId: string) => {\n    const poll = async () => {\n      try {\n        const response = await fetch(`/api/download?id=${downloadId}`);\n        const data = await response.json();\n\n        if (data.success) {\n          setActiveDownloads(prev =>\n            prev.map(download =>\n              download.id === downloadId\n                ? { ...download, ...data.data }\n                : download\n            )\n          );\n\n          // If download is completed or failed, stop polling\n          if (data.data.status === 'completed' || data.data.status === 'failed') {\n            if (data.data.status === 'completed') {\n              // Refresh download history\n              fetchDownloadHistory();\n            }\n            return;\n          }\n\n          // Continue polling\n          setTimeout(poll, 2000);\n        }\n      } catch (err) {\n        console.error('Error polling download progress:', err);\n      }\n    };\n\n    poll();\n  };\n\n  const fetchDownloadHistory = async () => {\n    try {\n      const response = await fetch('/api/downloads');\n      const data = await response.json();\n\n      if (data.success) {\n        setDownloadHistory(data.data);\n      }\n    } catch (err) {\n      console.error('Error fetching download history:', err);\n    }\n  };\n\n  const deleteDownload = async (filename: string) => {\n    try {\n      const response = await fetch('/api/downloads', {\n        method: 'DELETE',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ filename }),\n      });\n\n      if (response.ok) {\n        fetchDownloadHistory();\n      }\n    } catch (err) {\n      console.error('Error deleting download:', err);\n    }\n  };\n\n  const handleBatchDownload = async (urls: string[], options: any) => {\n    try {\n      const response = await fetch('/api/batch-download', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ urls, options }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to start batch download');\n      }\n\n      if (data.success) {\n        // Add to active batch downloads\n        const newBatchDownload = {\n          id: data.batchId,\n          status: 'downloading',\n          progress: 0,\n          urls: urls,\n          startTime: Date.now(),\n          totalItems: data.totalUrls,\n          completedItems: 0,\n          currentItem: '',\n          files: []\n        };\n\n        setActiveBatchDownloads(prev => [...prev, newBatchDownload]);\n\n        // Start polling for progress\n        pollBatchDownloadProgress(data.batchId);\n      }\n    } catch (err: any) {\n      setError(err.message || 'Failed to start batch download');\n    }\n  };\n\n  const pollBatchDownloadProgress = async (batchId: string) => {\n    const poll = async () => {\n      try {\n        const response = await fetch(`/api/batch-download?id=${batchId}`);\n        const data = await response.json();\n\n        if (data.success) {\n          setActiveBatchDownloads(prev =>\n            prev.map(batch =>\n              batch.id === batchId\n                ? { ...batch, ...data.data }\n                : batch\n            )\n          );\n\n          // If batch is completed or failed, stop polling\n          if (data.data.status === 'completed' || data.data.status === 'failed') {\n            if (data.data.status === 'completed') {\n              // Refresh download history\n              fetchDownloadHistory();\n            }\n            return;\n          }\n\n          // Continue polling\n          setTimeout(poll, 3000);\n        }\n      } catch (err) {\n        console.error('Error polling batch download progress:', err);\n      }\n    };\n\n    poll();\n  };\n\n  // Fetch download history on component mount\n  useEffect(() => {\n    fetchDownloadHistory();\n  }, []);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    fetchVideoInfo();\n  };\n\n  // Queue Management Functions\n  const handlePauseDownload = (id: string) => {\n    console.log('Pausing download:', id);\n  };\n\n  const handleResumeDownload = (id: string) => {\n    console.log('Resuming download:', id);\n  };\n\n  const handleCancelDownload = (id: string) => {\n    setDownloadQueue(prev => prev.filter(item => item.id !== id));\n    setActiveDownloads(prev => prev.filter(item => item.id !== id));\n  };\n\n  const handleRetryDownload = (id: string) => {\n    console.log('Retrying download:', id);\n  };\n\n  const handlePriorityChange = (id: string, priority: 'low' | 'normal' | 'high') => {\n    setDownloadQueue(prev =>\n      prev.map(item =>\n        item.id === id ? { ...item, priority } : item\n      )\n    );\n  };\n\n  const handleQueueReorder = (fromIndex: number, toIndex: number) => {\n    setDownloadQueue(prev => {\n      const newQueue = [...prev];\n      const [removed] = newQueue.splice(fromIndex, 1);\n      newQueue.splice(toIndex, 0, removed);\n      return newQueue;\n    });\n  };\n\n  const handleSettingsChange = (newSettings: any) => {\n    setSettings(newSettings);\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-8\">\n      {/* Batch Downloader */}\n      <BatchDownloader onBatchDownload={handleBatchDownload} />\n\n      {/* URL Input Form */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div>\n            <label htmlFor=\"url\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Video URL\n            </label>\n            <div className=\"flex gap-2\">\n              <input\n                type=\"url\"\n                id=\"url\"\n                value={url}\n                onChange={(e) => setUrl(e.target.value)}\n                placeholder=\"Enter video URL (YouTube, Vimeo, etc.)\"\n                className=\"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n                disabled={loading}\n              />\n              <button\n                type=\"submit\"\n                disabled={loading || !url.trim()}\n                className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                {loading ? 'Analyzing...' : 'Analyze'}\n              </button>\n            </div>\n          </div>\n        </form>\n\n        {error && (\n          <div className=\"mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\">\n            <p className=\"text-red-800 dark:text-red-200\">{error}</p>\n          </div>\n        )}\n      </div>\n\n      {/* Video Information */}\n      {videoInfo && (\n        <VideoInfoCard videoInfo={videoInfo} onDownload={startDownload} />\n      )}\n\n      {/* Active Downloads */}\n      {activeDownloads.length > 0 && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white mb-4\">\n            Active Downloads\n          </h3>\n          <div className=\"space-y-4\">\n            {activeDownloads.map((download) => (\n              <DownloadProgress key={download.id} download={download} />\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Active Batch Downloads */}\n      {activeBatchDownloads.length > 0 && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white mb-4\">\n            📦 Active Batch Downloads\n          </h3>\n          <div className=\"space-y-4\">\n            {activeBatchDownloads.map((batch) => (\n              <div key={batch.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <div className=\"flex-1 min-w-0\">\n                    <p className=\"text-sm font-medium text-gray-800 dark:text-white\">\n                      Batch Download ({batch.completedItems}/{batch.totalItems} items)\n                    </p>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      {batch.currentItem || 'Preparing...'}\n                    </p>\n                  </div>\n                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white ${\n                    batch.status === 'downloading' ? 'bg-blue-600' :\n                    batch.status === 'completed' ? 'bg-green-600' : 'bg-red-600'\n                  }`}>\n                    {batch.status === 'downloading' ? 'Downloading...' :\n                     batch.status === 'completed' ? 'Completed' : 'Failed'}\n                  </span>\n                </div>\n\n                <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2\">\n                  <div\n                    className={`h-2 rounded-full transition-all duration-300 ${\n                      batch.status === 'downloading' ? 'bg-blue-600' :\n                      batch.status === 'completed' ? 'bg-green-600' : 'bg-red-600'\n                    }`}\n                    style={{ width: `${Math.max(0, Math.min(100, batch.progress))}%` }}\n                  ></div>\n                </div>\n\n                <div className=\"flex justify-between items-center text-xs text-gray-500 dark:text-gray-400\">\n                  <span>{batch.progress.toFixed(1)}%</span>\n                  <span>{batch.urls.length} URL(s)</span>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Download History */}\n      <DownloadHistory \n        downloads={downloadHistory} \n        onDelete={deleteDownload}\n        onRefresh={fetchDownloadHistory}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AA4Ce,SAAS;;IACtB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC3E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAChE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;QAC5C,wBAAwB;QACxB,gBAAgB;QAChB,oBAAoB;QACpB,eAAe;QACf,0BAA0B;IAC5B;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,IAAI,IAAI,IAAI;YACf,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QACT,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,KAAK,IAAI,IAAI;gBAAG;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,IAAI,KAAK,OAAO,EAAE;gBAChB,aAAa,KAAK,IAAI;YACxB,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO,QAAiB,SAAkB,MAAe,aAAsB;QACnG,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,KAAK,UAAU,GAAG;oBAClB;oBACA;oBACA;oBACA;oBACA;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,IAAI,KAAK,OAAO,EAAE;gBAChB,0BAA0B;gBAC1B,MAAM,cAA8B;oBAClC,IAAI,KAAK,UAAU;oBACnB,QAAQ;oBACR,UAAU;oBACV,UAAU;oBACV,KAAK,UAAU,GAAG;oBAClB,WAAW,KAAK,GAAG;oBACnB,OAAO;gBACT;gBAEA,mBAAmB,CAAA,OAAQ;2BAAI;wBAAM;qBAAY;gBAEjD,6BAA6B;gBAC7B,qBAAqB,KAAK,UAAU;YACtC;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,MAAM,OAAO;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,YAAY;gBAC7D,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,mBAAmB,CAAA,OACjB,KAAK,GAAG,CAAC,CAAA,WACP,SAAS,EAAE,KAAK,aACZ;gCAAE,GAAG,QAAQ;gCAAE,GAAG,KAAK,IAAI;4BAAC,IAC5B;oBAIR,mDAAmD;oBACnD,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,eAAe,KAAK,IAAI,CAAC,MAAM,KAAK,UAAU;wBACrE,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,aAAa;4BACpC,2BAA2B;4BAC3B;wBACF;wBACA;oBACF;oBAEA,mBAAmB;oBACnB,WAAW,MAAM;gBACnB;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;QAEA;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,mBAAmB,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,kBAAkB;gBAC7C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,sBAAsB,OAAO,MAAgB;QACjD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM;gBAAQ;YACvC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,IAAI,KAAK,OAAO,EAAE;gBAChB,gCAAgC;gBAChC,MAAM,mBAAmB;oBACvB,IAAI,KAAK,OAAO;oBAChB,QAAQ;oBACR,UAAU;oBACV,MAAM;oBACN,WAAW,KAAK,GAAG;oBACnB,YAAY,KAAK,SAAS;oBAC1B,gBAAgB;oBAChB,aAAa;oBACb,OAAO,EAAE;gBACX;gBAEA,wBAAwB,CAAA,OAAQ;2BAAI;wBAAM;qBAAiB;gBAE3D,6BAA6B;gBAC7B,0BAA0B,KAAK,OAAO;YACxC;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B;IACF;IAEA,MAAM,4BAA4B,OAAO;QACvC,MAAM,OAAO;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,SAAS;gBAChE,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,wBAAwB,CAAA,OACtB,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,EAAE,KAAK,UACT;gCAAE,GAAG,KAAK;gCAAE,GAAG,KAAK,IAAI;4BAAC,IACzB;oBAIR,gDAAgD;oBAChD,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,eAAe,KAAK,IAAI,CAAC,MAAM,KAAK,UAAU;wBACrE,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,aAAa;4BACpC,2BAA2B;4BAC3B;wBACF;wBACA;oBACF;oBAEA,mBAAmB;oBACnB,WAAW,MAAM;gBACnB;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,0CAA0C;YAC1D;QACF;QAEA;IACF;IAEA,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB;IACF;IAEA,6BAA6B;IAC7B,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,GAAG,CAAC,qBAAqB;IACnC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,QAAQ,GAAG,CAAC,sBAAsB;IACpC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACzD,mBAAmB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAC7D;IAEA,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,GAAG,CAAC,sBAAsB;IACpC;IAEA,MAAM,uBAAuB,CAAC,IAAY;QACxC,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,KAAK;oBAAE,GAAG,IAAI;oBAAE;gBAAS,IAAI;IAG/C;IAEA,MAAM,qBAAqB,CAAC,WAAmB;QAC7C,iBAAiB,CAAA;YACf,MAAM,WAAW;mBAAI;aAAK;YAC1B,MAAM,CAAC,QAAQ,GAAG,SAAS,MAAM,CAAC,WAAW;YAC7C,SAAS,MAAM,CAAC,SAAS,GAAG;YAC5B,OAAO;QACT;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,wIAAA,CAAA,UAAe;gBAAC,iBAAiB;;;;;;0BAGlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,UAAU;wBAAc,WAAU;kCACtC,cAAA,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAM,WAAU;8CAAkE;;;;;;8CAGjG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK;4CACtC,aAAY;4CACZ,WAAU;4CACV,UAAU;;;;;;sDAEZ,6LAAC;4CACC,MAAK;4CACL,UAAU,WAAW,CAAC,IAAI,IAAI;4CAC9B,WAAU;sDAET,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;oBAMnC,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAkC;;;;;;;;;;;;;;;;;YAMpD,2BACC,6LAAC,sIAAA,CAAA,UAAa;gBAAC,WAAW;gBAAW,YAAY;;;;;;YAIlD,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,yBACpB,6LAAC,yIAAA,CAAA,UAAgB;gCAAmB,UAAU;+BAAvB,SAAS,EAAE;;;;;;;;;;;;;;;;YAOzC,qBAAqB,MAAM,GAAG,mBAC7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,6LAAC;wBAAI,WAAU;kCACZ,qBAAqB,GAAG,CAAC,CAAC,sBACzB,6LAAC;gCAAmB,WAAU;;kDAC5B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;;4DAAoD;4DAC9C,MAAM,cAAc;4DAAC;4DAAE,MAAM,UAAU;4DAAC;;;;;;;kEAE3D,6LAAC;wDAAE,WAAU;kEACV,MAAM,WAAW,IAAI;;;;;;;;;;;;0DAG1B,6LAAC;gDAAK,WAAW,CAAC,mFAAmF,EACnG,MAAM,MAAM,KAAK,gBAAgB,gBACjC,MAAM,MAAM,KAAK,cAAc,iBAAiB,cAChD;0DACC,MAAM,MAAM,KAAK,gBAAgB,mBACjC,MAAM,MAAM,KAAK,cAAc,cAAc;;;;;;;;;;;;kDAIlD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAW,CAAC,6CAA6C,EACvD,MAAM,MAAM,KAAK,gBAAgB,gBACjC,MAAM,MAAM,KAAK,cAAc,iBAAiB,cAChD;4CACF,OAAO;gDAAE,OAAO,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,MAAM,QAAQ,GAAG,CAAC,CAAC;4CAAC;;;;;;;;;;;kDAIrE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;oDAAM,MAAM,QAAQ,CAAC,OAAO,CAAC;oDAAG;;;;;;;0DACjC,6LAAC;;oDAAM,MAAM,IAAI,CAAC,MAAM;oDAAC;;;;;;;;;;;;;;+BA/BnB,MAAM,EAAE;;;;;;;;;;;;;;;;0BAwC1B,6LAAC,wIAAA,CAAA,UAAe;gBACd,WAAW;gBACX,UAAU;gBACV,WAAW;;;;;;;;;;;;AAInB;GAtZwB;KAAA", "debugId": null}}, {"offset": {"line": 2691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport VideoDownloader from '@/components/VideoDownloader';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <header className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-800 dark:text-white mb-2\">\n            Universal Video Downloader\n          </h1>\n          <p className=\"text-lg text-gray-600 dark:text-gray-300\">\n            Download videos from any website with ease\n          </p>\n          <div className=\"mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\">\n            <p className=\"text-sm text-yellow-800 dark:text-yellow-200\">\n              <strong>Legal Notice:</strong> Please respect copyright laws and only download videos you have permission to download.\n              This tool is intended for personal use, educational purposes, and downloading content you own or have explicit permission to download.\n            </p>\n          </div>\n        </header>\n\n        <main>\n          <VideoDownloader />\n        </main>\n\n        <footer className=\"mt-16 text-center text-gray-500 dark:text-gray-400\">\n          <p className=\"text-sm\">\n            Powered by yt-dlp • Built with Next.js and Tailwind CSS\n          </p>\n        </footer>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAO,WAAU;;sCAChB,6LAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,6LAAC;4BAAE,WAAU;sCAA2C;;;;;;sCAGxD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;kDACX,6LAAC;kDAAO;;;;;;oCAAsB;;;;;;;;;;;;;;;;;;8BAMpC,6LAAC;8BACC,cAAA,6LAAC,wIAAA,CAAA,UAAe;;;;;;;;;;8BAGlB,6LAAC;oBAAO,WAAU;8BAChB,cAAA,6LAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;;;;;;;;;;;;AAOjC;KA/BwB", "debugId": null}}]}