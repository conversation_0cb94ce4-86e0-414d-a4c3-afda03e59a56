{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/app/api/downloads/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport fs from 'fs';\nimport path from 'path';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const downloadsDir = path.join(process.cwd(), 'public', 'downloads');\n    \n    // Create downloads directory if it doesn't exist\n    if (!fs.existsSync(downloadsDir)) {\n      fs.mkdirSync(downloadsDir, { recursive: true });\n      return NextResponse.json({ success: true, data: [] });\n    }\n\n    // Read all files in downloads directory\n    const files = fs.readdirSync(downloadsDir);\n    \n    const downloadedFiles = files.map(filename => {\n      const filePath = path.join(downloadsDir, filename);\n      const stats = fs.statSync(filePath);\n      \n      return {\n        filename: filename,\n        size: stats.size,\n        downloadDate: stats.mtime,\n        downloadUrl: `/downloads/${filename}`\n      };\n    }).sort((a, b) => b.downloadDate.getTime() - a.downloadDate.getTime());\n\n    return NextResponse.json({\n      success: true,\n      data: downloadedFiles\n    });\n\n  } catch (error: any) {\n    console.error('Downloads list error:', error);\n    return NextResponse.json({\n      error: 'Failed to retrieve downloads',\n      details: error.message\n    }, { status: 500 });\n  }\n}\n\nexport async function DELETE(request: NextRequest) {\n  try {\n    const { filename } = await request.json();\n\n    if (!filename) {\n      return NextResponse.json({ error: 'Filename is required' }, { status: 400 });\n    }\n\n    const downloadsDir = path.join(process.cwd(), 'public', 'downloads');\n    const filePath = path.join(downloadsDir, filename);\n\n    // Security check: ensure the file is within the downloads directory\n    if (!filePath.startsWith(downloadsDir)) {\n      return NextResponse.json({ error: 'Invalid file path' }, { status: 400 });\n    }\n\n    // Check if file exists\n    if (!fs.existsSync(filePath)) {\n      return NextResponse.json({ error: 'File not found' }, { status: 404 });\n    }\n\n    // Delete the file\n    fs.unlinkSync(filePath);\n\n    return NextResponse.json({\n      success: true,\n      message: 'File deleted successfully'\n    });\n\n  } catch (error: any) {\n    console.error('Delete file error:', error);\n    return NextResponse.json({\n      error: 'Failed to delete file',\n      details: error.message\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;QAExD,iDAAiD;QACjD,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,eAAe;YAChC,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,cAAc;gBAAE,WAAW;YAAK;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,SAAS;gBAAM,MAAM,EAAE;YAAC;QACrD;QAEA,wCAAwC;QACxC,MAAM,QAAQ,6FAAA,CAAA,UAAE,CAAC,WAAW,CAAC;QAE7B,MAAM,kBAAkB,MAAM,GAAG,CAAC,CAAA;YAChC,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,cAAc;YACzC,MAAM,QAAQ,6FAAA,CAAA,UAAE,CAAC,QAAQ,CAAC;YAE1B,OAAO;gBACL,UAAU;gBACV,MAAM,MAAM,IAAI;gBAChB,cAAc,MAAM,KAAK;gBACzB,aAAa,CAAC,WAAW,EAAE,UAAU;YACvC;QACF,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,CAAC,OAAO,KAAK,EAAE,YAAY,CAAC,OAAO;QAEnE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,SAAS,MAAM,OAAO;QACxB,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,OAAO,OAAoB;IAC/C,IAAI;QACF,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEvC,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAuB,GAAG;gBAAE,QAAQ;YAAI;QAC5E;QAEA,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;QACxD,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,cAAc;QAEzC,oEAAoE;QACpE,IAAI,CAAC,SAAS,UAAU,CAAC,eAAe;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAoB,GAAG;gBAAE,QAAQ;YAAI;QACzE;QAEA,uBAAuB;QACvB,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,WAAW;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,kBAAkB;QAClB,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC;QAEd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IAEF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,SAAS,MAAM,OAAO;QACxB,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}