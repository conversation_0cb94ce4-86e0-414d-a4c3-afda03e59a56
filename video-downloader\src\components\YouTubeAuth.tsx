'use client';

import { useState, useEffect } from 'react';
import { signIn, signOut, useSession } from 'next-auth/react';
import SetupGuide from './SetupGuide';

interface YouTubeAuthProps {
  onAuthChange?: (isAuthenticated: boolean, userData?: any) => void;
}

export default function YouTubeAuth({ onAuthChange }: YouTubeAuthProps) {
  const { data: session, status } = useSession();
  const [userContent, setUserContent] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (session?.accessToken) {
      onAuthChange?.(true, session.user);
      fetchUserContent();
    } else {
      onAuthChange?.(false);
      setUserContent(null);
    }
  }, [session]);

  const fetchUserContent = async () => {
    if (!session?.accessToken) return;

    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/youtube/purchased');
      const data = await response.json();

      if (!response.ok) {
        if (data.requiresReauth) {
          setError('Please re-authenticate to access your YouTube content');
          return;
        }
        throw new Error(data.message || 'Failed to fetch content');
      }

      setUserContent(data.data);
    } catch (err: any) {
      setError(err.message || 'Failed to load your YouTube content');
    } finally {
      setLoading(false);
    }
  };

  const handleSignIn = () => {
    signIn('google', {
      callbackUrl: window.location.href,
    });
  };

  const handleSignOut = () => {
    signOut({
      callbackUrl: window.location.href,
    });
    setUserContent(null);
  };

  if (status === 'loading') {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="flex items-center space-x-3">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="text-gray-600 dark:text-gray-300">Loading authentication...</span>
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="text-center">
          <div className="mb-4">
            <div className="mx-auto w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">
              🔐 YouTube Authentication
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Sign in with your YouTube account to access and download your purchased content, private videos, and playlists.
            </p>
          </div>

          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
            <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
              🎯 What you can access:
            </h4>
            <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <li>• Your purchased movies and TV shows</li>
              <li>• Private videos you've uploaded</li>
              <li>• Your personal playlists</li>
              <li>• Channel content you own</li>
              <li>• Premium quality downloads</li>
            </ul>
          </div>

          <button
            onClick={handleSignIn}
            className="w-full px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors flex items-center justify-center space-x-2"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            <span>Sign in with Google/YouTube</span>
          </button>

          <p className="text-xs text-gray-500 dark:text-gray-400 mt-4">
            We only access content you own or have purchased. Your authentication is secure and can be revoked at any time.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
              ✅ Authenticated as {session.user?.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              {session.user?.email}
            </p>
          </div>
        </div>
        <button
          onClick={handleSignOut}
          className="px-4 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
        >
          Sign Out
        </button>
      </div>

      {loading && (
        <div className="flex items-center space-x-3 text-sm text-gray-600 dark:text-gray-300 mb-4">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          <span>Loading your YouTube content...</span>
        </div>
      )}

      {error && (
        <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-red-800 dark:text-red-200 text-sm">{error}</p>
          <button
            onClick={fetchUserContent}
            className="mt-2 text-sm text-red-600 dark:text-red-400 hover:underline"
          >
            Try again
          </button>
        </div>
      )}

      {userContent && (
        <div className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {userContent.totalPurchased}
              </div>
              <div className="text-sm text-blue-800 dark:text-blue-200">
                Purchased Movies
              </div>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {userContent.totalPrivateVideos}
              </div>
              <div className="text-sm text-green-800 dark:text-green-200">
                Private Videos
              </div>
            </div>
            <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {userContent.totalPlaylists}
              </div>
              <div className="text-sm text-purple-800 dark:text-purple-200">
                Playlists
              </div>
            </div>
            <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                {userContent.channel?.videoCount || 0}
              </div>
              <div className="text-sm text-orange-800 dark:text-orange-200">
                Your Videos
              </div>
            </div>
          </div>

          {userContent.channel && (
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-800 dark:text-white mb-2">
                📺 Your Channel: {userContent.channel.title}
              </h4>
              <div className="text-xs text-gray-600 dark:text-gray-300 space-y-1">
                <div>Subscribers: {parseInt(userContent.channel.subscriberCount || 0).toLocaleString()}</div>
                <div>Total Views: {parseInt(userContent.channel.viewCount || 0).toLocaleString()}</div>
              </div>
            </div>
          )}

          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <h4 className="text-sm font-medium text-green-800 dark:text-green-200 mb-2">
              🎯 Ready for Authenticated Downloads
            </h4>
            <p className="text-xs text-green-700 dark:text-green-300">
              You can now download your purchased content, private videos, and playlists with full quality and metadata.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
