{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/VideoInfoCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Image from 'next/image';\n\ninterface VideoInfo {\n  title: string;\n  description: string;\n  duration: number;\n  uploader: string;\n  upload_date: string;\n  view_count: number;\n  thumbnail: string;\n  formats: Array<{\n    format_id: string;\n    ext: string;\n    quality: string;\n    filesize: number;\n    format_note: string;\n    vcodec: string;\n    acodec: string;\n    fps: number;\n    resolution: string;\n  }>;\n  url: string;\n  webpage_url: string;\n  extractor: string;\n}\n\ninterface VideoInfoCardProps {\n  videoInfo: VideoInfo;\n  onDownload: (format?: string, quality?: string) => void;\n}\n\nexport default function VideoInfoCard({ videoInfo, onDownload }: VideoInfoCardProps) {\n  const [selectedFormat, setSelectedFormat] = useState('best');\n  const [selectedQuality, setSelectedQuality] = useState('');\n\n  const formatDuration = (seconds: number) => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const formatFileSize = (bytes: number) => {\n    if (!bytes) return 'Unknown';\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n  };\n\n  const formatDate = (dateString: string) => {\n    if (!dateString) return 'Unknown';\n    const year = dateString.substring(0, 4);\n    const month = dateString.substring(4, 6);\n    const day = dateString.substring(6, 8);\n    return `${year}-${month}-${day}`;\n  };\n\n  const formatViewCount = (count: number) => {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  };\n\n  // Group formats by quality for better display\n  const videoFormats = videoInfo.formats\n    .filter(f => f.vcodec && f.vcodec !== 'none')\n    .sort((a, b) => {\n      const aHeight = parseInt(a.resolution?.split('x')[1] || '0');\n      const bHeight = parseInt(b.resolution?.split('x')[1] || '0');\n      return bHeight - aHeight;\n    });\n\n  const audioFormats = videoInfo.formats\n    .filter(f => f.acodec && f.acodec !== 'none' && (!f.vcodec || f.vcodec === 'none'))\n    .sort((a, b) => (b.filesize || 0) - (a.filesize || 0));\n\n  const handleDownload = () => {\n    if (selectedFormat === 'best') {\n      onDownload();\n    } else if (selectedFormat === 'quality' && selectedQuality) {\n      onDownload(undefined, selectedQuality);\n    } else {\n      onDownload(selectedFormat);\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden\">\n      <div className=\"md:flex\">\n        {/* Thumbnail */}\n        <div className=\"md:w-1/3\">\n          {videoInfo.thumbnail && (\n            <div className=\"relative aspect-video\">\n              <Image\n                src={videoInfo.thumbnail}\n                alt={videoInfo.title}\n                fill\n                className=\"object-cover\"\n                unoptimized\n              />\n            </div>\n          )}\n        </div>\n\n        {/* Video Info */}\n        <div className=\"md:w-2/3 p-6\">\n          <h2 className=\"text-xl font-bold text-gray-800 dark:text-white mb-2\">\n            {videoInfo.title}\n          </h2>\n          \n          <div className=\"grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-300 mb-4\">\n            <div>\n              <span className=\"font-medium\">Uploader:</span> {videoInfo.uploader}\n            </div>\n            <div>\n              <span className=\"font-medium\">Duration:</span> {formatDuration(videoInfo.duration)}\n            </div>\n            <div>\n              <span className=\"font-medium\">Views:</span> {formatViewCount(videoInfo.view_count)}\n            </div>\n            <div>\n              <span className=\"font-medium\">Upload Date:</span> {formatDate(videoInfo.upload_date)}\n            </div>\n            <div>\n              <span className=\"font-medium\">Source:</span> {videoInfo.extractor}\n            </div>\n          </div>\n\n          {videoInfo.description && (\n            <div className=\"mb-4\">\n              <p className=\"text-sm text-gray-600 dark:text-gray-300 line-clamp-3\">\n                {videoInfo.description.substring(0, 200)}\n                {videoInfo.description.length > 200 && '...'}\n              </p>\n            </div>\n          )}\n\n          {/* Download Options */}\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Download Options\n              </label>\n              \n              <div className=\"space-y-2\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    name=\"format\"\n                    value=\"best\"\n                    checked={selectedFormat === 'best'}\n                    onChange={(e) => setSelectedFormat(e.target.value)}\n                    className=\"mr-2\"\n                  />\n                  <span className=\"text-sm\">Best Quality (Automatic)</span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    name=\"format\"\n                    value=\"quality\"\n                    checked={selectedFormat === 'quality'}\n                    onChange={(e) => setSelectedFormat(e.target.value)}\n                    className=\"mr-2\"\n                  />\n                  <span className=\"text-sm\">Select Quality:</span>\n                  <select\n                    value={selectedQuality}\n                    onChange={(e) => {\n                      setSelectedQuality(e.target.value);\n                      setSelectedFormat('quality');\n                    }}\n                    className=\"ml-2 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm dark:bg-gray-700 dark:text-white\"\n                    disabled={selectedFormat !== 'quality'}\n                  >\n                    <option value=\"\">Select...</option>\n                    <option value=\"720\">720p</option>\n                    <option value=\"480\">480p</option>\n                    <option value=\"360\">360p</option>\n                    <option value=\"240\">240p</option>\n                  </select>\n                </label>\n\n                {videoFormats.length > 0 && (\n                  <details className=\"mt-2\">\n                    <summary className=\"cursor-pointer text-sm text-blue-600 dark:text-blue-400\">\n                      Advanced Format Selection\n                    </summary>\n                    <div className=\"mt-2 max-h-40 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded p-2\">\n                      {videoFormats.slice(0, 10).map((format) => (\n                        <label key={format.format_id} className=\"flex items-center mb-1\">\n                          <input\n                            type=\"radio\"\n                            name=\"format\"\n                            value={format.format_id}\n                            checked={selectedFormat === format.format_id}\n                            onChange={(e) => setSelectedFormat(e.target.value)}\n                            className=\"mr-2\"\n                          />\n                          <span className=\"text-xs\">\n                            {format.resolution} • {format.ext} • {formatFileSize(format.filesize)} • {format.format_note}\n                          </span>\n                        </label>\n                      ))}\n                    </div>\n                  </details>\n                )}\n              </div>\n            </div>\n\n            <button\n              onClick={handleDownload}\n              className=\"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors\"\n            >\n              Start Download\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAkCe,SAAS,cAAc,EAAE,SAAS,EAAE,UAAU,EAAsB;;IACjF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,iBAAiB,CAAC;QACtB,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;QAC9C,MAAM,OAAO,UAAU;QAEvB,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;QAC9F;QACA,OAAO,GAAG,QAAQ,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACzD;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,OAAO,OAAO;QACnB,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,CAAC,EAAE;IAC3E;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,MAAM,OAAO,WAAW,SAAS,CAAC,GAAG;QACrC,MAAM,QAAQ,WAAW,SAAS,CAAC,GAAG;QACtC,MAAM,MAAM,WAAW,SAAS,CAAC,GAAG;QACpC,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK;IAClC;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,SAAS,SAAS;YACpB,OAAO,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,KAAK;QACxC,OAAO,IAAI,SAAS,MAAM;YACxB,OAAO,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,KAAK;QACrC;QACA,OAAO,MAAM,QAAQ;IACvB;IAEA,8CAA8C;IAC9C,MAAM,eAAe,UAAU,OAAO,CACnC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,IAAI,EAAE,MAAM,KAAK,QACrC,IAAI,CAAC,CAAC,GAAG;QACR,MAAM,UAAU,SAAS,EAAE,UAAU,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;QACxD,MAAM,UAAU,SAAS,EAAE,UAAU,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;QACxD,OAAO,UAAU;IACnB;IAEF,MAAM,eAAe,UAAU,OAAO,CACnC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,IAAI,EAAE,MAAM,KAAK,UAAU,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE,MAAM,KAAK,MAAM,GAChF,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,IAAI,CAAC;IAEtD,MAAM,iBAAiB;QACrB,IAAI,mBAAmB,QAAQ;YAC7B;QACF,OAAO,IAAI,mBAAmB,aAAa,iBAAiB;YAC1D,WAAW,WAAW;QACxB,OAAO;YACL,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACZ,UAAU,SAAS,kBAClB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,UAAU,SAAS;4BACxB,KAAK,UAAU,KAAK;4BACpB,IAAI;4BACJ,WAAU;4BACV,WAAW;;;;;;;;;;;;;;;;8BAOnB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,UAAU,KAAK;;;;;;sCAGlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAgB;wCAAE,UAAU,QAAQ;;;;;;;8CAEpE,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAgB;wCAAE,eAAe,UAAU,QAAQ;;;;;;;8CAEnF,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAa;wCAAE,gBAAgB,UAAU,UAAU;;;;;;;8CAEnF,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAmB;wCAAE,WAAW,UAAU,WAAW;;;;;;;8CAErF,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAc;wCAAE,UAAU,SAAS;;;;;;;;;;;;;wBAIpE,UAAU,WAAW,kBACpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;oCACV,UAAU,WAAW,CAAC,SAAS,CAAC,GAAG;oCACnC,UAAU,WAAW,CAAC,MAAM,GAAG,OAAO;;;;;;;;;;;;sCAM7C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAInF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,mBAAmB;4DAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4DACjD,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAG5B,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,mBAAmB;4DAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4DACjD,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC;4DACC,OAAO;4DACP,UAAU,CAAC;gEACT,mBAAmB,EAAE,MAAM,CAAC,KAAK;gEACjC,kBAAkB;4DACpB;4DACA,WAAU;4DACV,UAAU,mBAAmB;;8EAE7B,6LAAC;oEAAO,OAAM;8EAAG;;;;;;8EACjB,6LAAC;oEAAO,OAAM;8EAAM;;;;;;8EACpB,6LAAC;oEAAO,OAAM;8EAAM;;;;;;8EACpB,6LAAC;oEAAO,OAAM;8EAAM;;;;;;8EACpB,6LAAC;oEAAO,OAAM;8EAAM;;;;;;;;;;;;;;;;;;gDAIvB,aAAa,MAAM,GAAG,mBACrB,6LAAC;oDAAQ,WAAU;;sEACjB,6LAAC;4DAAQ,WAAU;sEAA0D;;;;;;sEAG7E,6LAAC;4DAAI,WAAU;sEACZ,aAAa,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,uBAC9B,6LAAC;oEAA6B,WAAU;;sFACtC,6LAAC;4EACC,MAAK;4EACL,MAAK;4EACL,OAAO,OAAO,SAAS;4EACvB,SAAS,mBAAmB,OAAO,SAAS;4EAC5C,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4EACjD,WAAU;;;;;;sFAEZ,6LAAC;4EAAK,WAAU;;gFACb,OAAO,UAAU;gFAAC;gFAAI,OAAO,GAAG;gFAAC;gFAAI,eAAe,OAAO,QAAQ;gFAAE;gFAAI,OAAO,WAAW;;;;;;;;mEAVpF,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAoBxC,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAtMwB;KAAA", "debugId": null}}, {"offset": {"line": 479, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/DownloadProgress.tsx"], "sourcesContent": ["'use client';\n\ninterface DownloadStatus {\n  id: string;\n  status: 'downloading' | 'completed' | 'failed';\n  progress: number;\n  filename: string | null;\n  url: string;\n  startTime: number;\n  error: string | null;\n}\n\ninterface DownloadProgressProps {\n  download: DownloadStatus;\n}\n\nexport default function DownloadProgress({ download }: DownloadProgressProps) {\n  const getStatusColor = () => {\n    switch (download.status) {\n      case 'downloading':\n        return 'bg-blue-600';\n      case 'completed':\n        return 'bg-green-600';\n      case 'failed':\n        return 'bg-red-600';\n      default:\n        return 'bg-gray-600';\n    }\n  };\n\n  const getStatusText = () => {\n    switch (download.status) {\n      case 'downloading':\n        return 'Downloading...';\n      case 'completed':\n        return 'Completed';\n      case 'failed':\n        return 'Failed';\n      default:\n        return 'Unknown';\n    }\n  };\n\n  const formatElapsedTime = () => {\n    const elapsed = Date.now() - download.startTime;\n    const seconds = Math.floor(elapsed / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const hours = Math.floor(minutes / 60);\n\n    if (hours > 0) {\n      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;\n    } else if (minutes > 0) {\n      return `${minutes}m ${seconds % 60}s`;\n    } else {\n      return `${seconds}s`;\n    }\n  };\n\n  const getDisplayUrl = () => {\n    try {\n      const url = new URL(download.url);\n      return url.hostname;\n    } catch {\n      return download.url.substring(0, 50) + '...';\n    }\n  };\n\n  return (\n    <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n      <div className=\"flex items-center justify-between mb-2\">\n        <div className=\"flex-1 min-w-0\">\n          <p className=\"text-sm font-medium text-gray-800 dark:text-white truncate\">\n            {download.filename || 'Preparing download...'}\n          </p>\n          <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\n            {getDisplayUrl()}\n          </p>\n        </div>\n        <div className=\"ml-4 flex items-center space-x-2\">\n          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white ${getStatusColor()}`}>\n            {getStatusText()}\n          </span>\n          <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n            {formatElapsedTime()}\n          </span>\n        </div>\n      </div>\n\n      {/* Progress Bar */}\n      <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2\">\n        <div\n          className={`h-2 rounded-full transition-all duration-300 ${getStatusColor()}`}\n          style={{ width: `${Math.max(0, Math.min(100, download.progress))}%` }}\n        ></div>\n      </div>\n\n      <div className=\"flex justify-between items-center\">\n        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n          {download.progress.toFixed(1)}%\n        </span>\n        {download.status === 'completed' && download.filename && (\n          <a\n            href={`/downloads/${download.filename}`}\n            download\n            className=\"text-xs text-blue-600 dark:text-blue-400 hover:underline\"\n          >\n            Download File\n          </a>\n        )}\n      </div>\n\n      {download.error && (\n        <div className=\"mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded\">\n          <p className=\"text-xs text-red-800 dark:text-red-200\">\n            Error: {download.error}\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAgBe,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;IAC1E,MAAM,iBAAiB;QACrB,OAAQ,SAAS,MAAM;YACrB,KAAK;gBACH,OAAO;YACT,KAAK;gBA<PERSON>,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ,SAAS,MAAM;YACrB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,UAAU,KAAK,GAAG,KAAK,SAAS,SAAS;QAC/C,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QAEnC,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,EAAE,EAAE,UAAU,GAAG,EAAE,EAAE,UAAU,GAAG,CAAC,CAAC;QACtD,OAAO,IAAI,UAAU,GAAG;YACtB,OAAO,GAAG,QAAQ,EAAE,EAAE,UAAU,GAAG,CAAC,CAAC;QACvC,OAAO;YACL,OAAO,GAAG,QAAQ,CAAC,CAAC;QACtB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,MAAM,IAAI,IAAI,SAAS,GAAG;YAChC,OAAO,IAAI,QAAQ;QACrB,EAAE,OAAM;YACN,OAAO,SAAS,GAAG,CAAC,SAAS,CAAC,GAAG,MAAM;QACzC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CACV,SAAS,QAAQ,IAAI;;;;;;0CAExB,6LAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;kCAGL,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAW,CAAC,mFAAmF,EAAE,kBAAkB;0CACtH;;;;;;0CAEH,6LAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;;;;;;;0BAMP,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAW,CAAC,6CAA6C,EAAE,kBAAkB;oBAC7E,OAAO;wBAAE,OAAO,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,SAAS,QAAQ,GAAG,CAAC,CAAC;oBAAC;;;;;;;;;;;0BAIxE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;;4BACb,SAAS,QAAQ,CAAC,OAAO,CAAC;4BAAG;;;;;;;oBAE/B,SAAS,MAAM,KAAK,eAAe,SAAS,QAAQ,kBACnD,6LAAC;wBACC,MAAM,CAAC,WAAW,EAAE,SAAS,QAAQ,EAAE;wBACvC,QAAQ;wBACR,WAAU;kCACX;;;;;;;;;;;;YAMJ,SAAS,KAAK,kBACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;wBAAyC;wBAC5C,SAAS,KAAK;;;;;;;;;;;;;;;;;;AAMlC;KAxGwB", "debugId": null}}, {"offset": {"line": 677, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/DownloadHistory.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface DownloadFile {\n  filename: string;\n  size: number;\n  downloadDate: string;\n  downloadUrl: string;\n}\n\ninterface DownloadHistoryProps {\n  downloads: DownloadFile[];\n  onDelete: (filename: string) => void;\n  onRefresh: () => void;\n}\n\nexport default function DownloadHistory({ downloads, onDelete, onRefresh }: DownloadHistoryProps) {\n  const [deletingFiles, setDeletingFiles] = useState<Set<string>>(new Set());\n\n  const formatFileSize = (bytes: number) => {\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n  };\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();\n  };\n\n  const handleDelete = async (filename: string) => {\n    if (confirm(`Are you sure you want to delete \"${filename}\"?`)) {\n      setDeletingFiles(prev => new Set(prev).add(filename));\n      try {\n        await onDelete(filename);\n      } finally {\n        setDeletingFiles(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(filename);\n          return newSet;\n        });\n      }\n    }\n  };\n\n  const getFileExtension = (filename: string) => {\n    return filename.split('.').pop()?.toLowerCase() || '';\n  };\n\n  const getFileIcon = (filename: string) => {\n    const ext = getFileExtension(filename);\n    switch (ext) {\n      case 'mp4':\n      case 'avi':\n      case 'mov':\n      case 'mkv':\n      case 'webm':\n        return '🎥';\n      case 'mp3':\n      case 'wav':\n      case 'flac':\n      case 'm4a':\n        return '🎵';\n      default:\n        return '📄';\n    }\n  };\n\n  if (downloads.length === 0) {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white\">\n            Download History\n          </h3>\n          <button\n            onClick={onRefresh}\n            className=\"px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n          >\n            Refresh\n          </button>\n        </div>\n        <div className=\"text-center py-8\">\n          <p className=\"text-gray-500 dark:text-gray-400\">No downloads yet</p>\n          <p className=\"text-sm text-gray-400 dark:text-gray-500 mt-1\">\n            Downloaded files will appear here\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white\">\n          Download History ({downloads.length})\n        </h3>\n        <button\n          onClick={onRefresh}\n          className=\"px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n        >\n          Refresh\n        </button>\n      </div>\n\n      <div className=\"space-y-3\">\n        {downloads.map((download) => (\n          <div\n            key={download.filename}\n            className=\"flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <div className=\"flex items-center space-x-3 flex-1 min-w-0\">\n              <span className=\"text-2xl\">{getFileIcon(download.filename)}</span>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-800 dark:text-white truncate\">\n                  {download.filename}\n                </p>\n                <div className=\"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400\">\n                  <span>{formatFileSize(download.size)}</span>\n                  <span>{formatDate(download.downloadDate)}</span>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-2 ml-4\">\n              <a\n                href={download.downloadUrl}\n                download\n                className=\"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\"\n              >\n                Download\n              </a>\n              <button\n                onClick={() => handleDelete(download.filename)}\n                disabled={deletingFiles.has(download.filename)}\n                className=\"px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                {deletingFiles.has(download.filename) ? 'Deleting...' : 'Delete'}\n              </button>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {downloads.length > 5 && (\n        <div className=\"mt-4 text-center\">\n          <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n            Showing {downloads.length} downloads\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAiBe,SAAS,gBAAgB,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAwB;;IAC9F,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAEpE,MAAM,iBAAiB,CAAC;QACtB,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,CAAC,EAAE;IAC3E;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,KAAK,MAAM,KAAK,kBAAkB;IAClE;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,QAAQ,CAAC,iCAAiC,EAAE,SAAS,EAAE,CAAC,GAAG;YAC7D,iBAAiB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC;YAC3C,IAAI;gBACF,MAAM,SAAS;YACjB,SAAU;gBACR,iBAAiB,CAAA;oBACf,MAAM,SAAS,IAAI,IAAI;oBACvB,OAAO,MAAM,CAAC;oBACd,OAAO;gBACT;YACF;QACF;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI,iBAAiB;IACrD;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,MAAM,iBAAiB;QAC7B,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAIH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;sCAChD,6LAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;;;;;;;IAMrE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAAsD;4BAC/C,UAAU,MAAM;4BAAC;;;;;;;kCAEtC,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAKH,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC;wBAEC,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAY,YAAY,SAAS,QAAQ;;;;;;kDACzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DACV,SAAS,QAAQ;;;;;;0DAEpB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAM,eAAe,SAAS,IAAI;;;;;;kEACnC,6LAAC;kEAAM,WAAW,SAAS,YAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAK7C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAM,SAAS,WAAW;wCAC1B,QAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,aAAa,SAAS,QAAQ;wCAC7C,UAAU,cAAc,GAAG,CAAC,SAAS,QAAQ;wCAC7C,WAAU;kDAET,cAAc,GAAG,CAAC,SAAS,QAAQ,IAAI,gBAAgB;;;;;;;;;;;;;uBA7BvD,SAAS,QAAQ;;;;;;;;;;YAoC3B,UAAU,MAAM,GAAG,mBAClB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;wBAA2C;wBAC7C,UAAU,MAAM;wBAAC;;;;;;;;;;;;;;;;;;AAMtC;GA1IwB;KAAA", "debugId": null}}, {"offset": {"line": 974, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/VideoDownloader.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport VideoInfoCard from './VideoInfoCard';\nimport DownloadProgress from './DownloadProgress';\nimport DownloadHistory from './DownloadHistory';\n\ninterface VideoInfo {\n  title: string;\n  description: string;\n  duration: number;\n  uploader: string;\n  upload_date: string;\n  view_count: number;\n  thumbnail: string;\n  formats: Array<{\n    format_id: string;\n    ext: string;\n    quality: string;\n    filesize: number;\n    format_note: string;\n    vcodec: string;\n    acodec: string;\n    fps: number;\n    resolution: string;\n  }>;\n  url: string;\n  webpage_url: string;\n  extractor: string;\n}\n\ninterface DownloadStatus {\n  id: string;\n  status: 'downloading' | 'completed' | 'failed';\n  progress: number;\n  filename: string | null;\n  url: string;\n  startTime: number;\n  error: string | null;\n}\n\nexport default function VideoDownloader() {\n  const [url, setUrl] = useState('');\n  const [videoInfo, setVideoInfo] = useState<VideoInfo | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [activeDownloads, setActiveDownloads] = useState<DownloadStatus[]>([]);\n  const [downloadHistory, setDownloadHistory] = useState<any[]>([]);\n\n  const fetchVideoInfo = async () => {\n    if (!url.trim()) {\n      setError('Please enter a valid URL');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    setVideoInfo(null);\n\n    try {\n      const response = await fetch('/api/video-info', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ url: url.trim() }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to fetch video information');\n      }\n\n      if (data.success) {\n        setVideoInfo(data.data);\n      } else {\n        throw new Error(data.error || 'Unknown error occurred');\n      }\n    } catch (err: any) {\n      setError(err.message || 'Failed to fetch video information');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const startDownload = async (format?: string, quality?: string) => {\n    if (!videoInfo) return;\n\n    try {\n      const response = await fetch('/api/download', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          url: videoInfo.url,\n          format,\n          quality,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to start download');\n      }\n\n      if (data.success) {\n        // Add to active downloads\n        const newDownload: DownloadStatus = {\n          id: data.downloadId,\n          status: 'downloading',\n          progress: 0,\n          filename: null,\n          url: videoInfo.url,\n          startTime: Date.now(),\n          error: null,\n        };\n\n        setActiveDownloads(prev => [...prev, newDownload]);\n        \n        // Start polling for progress\n        pollDownloadProgress(data.downloadId);\n      }\n    } catch (err: any) {\n      setError(err.message || 'Failed to start download');\n    }\n  };\n\n  const pollDownloadProgress = async (downloadId: string) => {\n    const poll = async () => {\n      try {\n        const response = await fetch(`/api/download?id=${downloadId}`);\n        const data = await response.json();\n\n        if (data.success) {\n          setActiveDownloads(prev =>\n            prev.map(download =>\n              download.id === downloadId\n                ? { ...download, ...data.data }\n                : download\n            )\n          );\n\n          // If download is completed or failed, stop polling\n          if (data.data.status === 'completed' || data.data.status === 'failed') {\n            if (data.data.status === 'completed') {\n              // Refresh download history\n              fetchDownloadHistory();\n            }\n            return;\n          }\n\n          // Continue polling\n          setTimeout(poll, 2000);\n        }\n      } catch (err) {\n        console.error('Error polling download progress:', err);\n      }\n    };\n\n    poll();\n  };\n\n  const fetchDownloadHistory = async () => {\n    try {\n      const response = await fetch('/api/downloads');\n      const data = await response.json();\n\n      if (data.success) {\n        setDownloadHistory(data.data);\n      }\n    } catch (err) {\n      console.error('Error fetching download history:', err);\n    }\n  };\n\n  const deleteDownload = async (filename: string) => {\n    try {\n      const response = await fetch('/api/downloads', {\n        method: 'DELETE',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ filename }),\n      });\n\n      if (response.ok) {\n        fetchDownloadHistory();\n      }\n    } catch (err) {\n      console.error('Error deleting download:', err);\n    }\n  };\n\n  // Fetch download history on component mount\n  useEffect(() => {\n    fetchDownloadHistory();\n  }, []);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    fetchVideoInfo();\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-8\">\n      {/* URL Input Form */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div>\n            <label htmlFor=\"url\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Video URL\n            </label>\n            <div className=\"flex gap-2\">\n              <input\n                type=\"url\"\n                id=\"url\"\n                value={url}\n                onChange={(e) => setUrl(e.target.value)}\n                placeholder=\"Enter video URL (YouTube, Vimeo, etc.)\"\n                className=\"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n                disabled={loading}\n              />\n              <button\n                type=\"submit\"\n                disabled={loading || !url.trim()}\n                className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                {loading ? 'Analyzing...' : 'Analyze'}\n              </button>\n            </div>\n          </div>\n        </form>\n\n        {error && (\n          <div className=\"mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\">\n            <p className=\"text-red-800 dark:text-red-200\">{error}</p>\n          </div>\n        )}\n      </div>\n\n      {/* Video Information */}\n      {videoInfo && (\n        <VideoInfoCard videoInfo={videoInfo} onDownload={startDownload} />\n      )}\n\n      {/* Active Downloads */}\n      {activeDownloads.length > 0 && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white mb-4\">\n            Active Downloads\n          </h3>\n          <div className=\"space-y-4\">\n            {activeDownloads.map((download) => (\n              <DownloadProgress key={download.id} download={download} />\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Download History */}\n      <DownloadHistory \n        downloads={downloadHistory} \n        onDelete={deleteDownload}\n        onRefresh={fetchDownloadHistory}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAyCe,SAAS;;IACtB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC3E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAEhE,MAAM,iBAAiB;QACrB,IAAI,CAAC,IAAI,IAAI,IAAI;YACf,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QACT,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,KAAK,IAAI,IAAI;gBAAG;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,IAAI,KAAK,OAAO,EAAE;gBAChB,aAAa,KAAK,IAAI;YACxB,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO,QAAiB;QAC5C,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,KAAK,UAAU,GAAG;oBAClB;oBACA;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,IAAI,KAAK,OAAO,EAAE;gBAChB,0BAA0B;gBAC1B,MAAM,cAA8B;oBAClC,IAAI,KAAK,UAAU;oBACnB,QAAQ;oBACR,UAAU;oBACV,UAAU;oBACV,KAAK,UAAU,GAAG;oBAClB,WAAW,KAAK,GAAG;oBACnB,OAAO;gBACT;gBAEA,mBAAmB,CAAA,OAAQ;2BAAI;wBAAM;qBAAY;gBAEjD,6BAA6B;gBAC7B,qBAAqB,KAAK,UAAU;YACtC;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,MAAM,OAAO;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,YAAY;gBAC7D,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,mBAAmB,CAAA,OACjB,KAAK,GAAG,CAAC,CAAA,WACP,SAAS,EAAE,KAAK,aACZ;gCAAE,GAAG,QAAQ;gCAAE,GAAG,KAAK,IAAI;4BAAC,IAC5B;oBAIR,mDAAmD;oBACnD,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,eAAe,KAAK,IAAI,CAAC,MAAM,KAAK,UAAU;wBACrE,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,aAAa;4BACpC,2BAA2B;4BAC3B;wBACF;wBACA;oBACF;oBAEA,mBAAmB;oBACnB,WAAW,MAAM;gBACnB;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;QAEA;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,mBAAmB,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,kBAAkB;gBAC7C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,UAAU;wBAAc,WAAU;kCACtC,cAAA,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAM,WAAU;8CAAkE;;;;;;8CAGjG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK;4CACtC,aAAY;4CACZ,WAAU;4CACV,UAAU;;;;;;sDAEZ,6LAAC;4CACC,MAAK;4CACL,UAAU,WAAW,CAAC,IAAI,IAAI;4CAC9B,WAAU;sDAET,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;oBAMnC,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAkC;;;;;;;;;;;;;;;;;YAMpD,2BACC,6LAAC,sIAAA,CAAA,UAAa;gBAAC,WAAW;gBAAW,YAAY;;;;;;YAIlD,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,yBACpB,6LAAC,yIAAA,CAAA,UAAgB;gCAAmB,UAAU;+BAAvB,SAAS,EAAE;;;;;;;;;;;;;;;;0BAO1C,6LAAC,wIAAA,CAAA,UAAe;gBACd,WAAW;gBACX,UAAU;gBACV,WAAW;;;;;;;;;;;;AAInB;GArOwB;KAAA", "debugId": null}}, {"offset": {"line": 1288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport VideoDownloader from '@/components/VideoDownloader';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <header className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-800 dark:text-white mb-2\">\n            Universal Video Downloader\n          </h1>\n          <p className=\"text-lg text-gray-600 dark:text-gray-300\">\n            Download videos from any website with ease\n          </p>\n          <div className=\"mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\">\n            <p className=\"text-sm text-yellow-800 dark:text-yellow-200\">\n              <strong>Legal Notice:</strong> Please respect copyright laws and only download videos you have permission to download.\n              This tool is intended for personal use, educational purposes, and downloading content you own or have explicit permission to download.\n            </p>\n          </div>\n        </header>\n\n        <main>\n          <VideoDownloader />\n        </main>\n\n        <footer className=\"mt-16 text-center text-gray-500 dark:text-gray-400\">\n          <p className=\"text-sm\">\n            Powered by yt-dlp • Built with Next.js and Tailwind CSS\n          </p>\n        </footer>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAO,WAAU;;sCAChB,6LAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,6LAAC;4BAAE,WAAU;sCAA2C;;;;;;sCAGxD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;kDACX,6LAAC;kDAAO;;;;;;oCAAsB;;;;;;;;;;;;;;;;;;8BAMpC,6LAAC;8BACC,cAAA,6LAAC,wIAAA,CAAA,UAAe;;;;;;;;;;8BAGlB,6LAAC;oBAAO,WAAU;8BAChB,cAAA,6LAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;;;;;;;;;;;;AAOjC;KA/BwB", "debugId": null}}]}