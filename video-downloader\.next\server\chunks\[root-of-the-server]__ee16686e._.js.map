{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/app/api/auth/test-redirect/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function GET(request: NextRequest) {\n  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';\n  const expectedRedirectUri = `${baseUrl}/api/auth/callback/google`;\n  \n  return NextResponse.json({\n    message: 'OAuth Redirect URI Test',\n    expectedRedirectUri: expectedRedirectUri,\n    instructions: [\n      '1. Copy the expectedRedirectUri below',\n      '2. Go to Google Cloud Console → APIs & Services → Credentials',\n      '3. Edit your OAuth 2.0 Client ID',\n      '4. In \"Authorized redirect URIs\", add EXACTLY the URI shown below',\n      '5. Remove any other redirect URIs',\n      '6. Save and wait 1-2 minutes',\n      '7. Test authentication again'\n    ],\n    copyThis: expectedRedirectUri,\n    troubleshooting: {\n      commonMistakes: [\n        'Using https:// instead of http:// for localhost',\n        'Adding trailing slash at the end',\n        'Wrong port number',\n        'Typos in the path'\n      ]\n    }\n  });\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,IAAI,OAAoB;IAC5C,MAAM,UAAU,QAAQ,GAAG,CAAC,YAAY,IAAI;IAC5C,MAAM,sBAAsB,GAAG,QAAQ,yBAAyB,CAAC;IAEjE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT,qBAAqB;QACrB,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,iBAAiB;YACf,gBAAgB;gBACd;gBACA;gBACA;gBACA;aACD;QACH;IACF;AACF", "debugId": null}}]}