import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
  }

  const diagnostics = {
    environment: process.env.NODE_ENV,
    nextAuthUrl: process.env.NEXTAUTH_URL,
    hasClientId: !!process.env.GOOGLE_CLIENT_ID,
    hasClientSecret: !!process.env.GOOGLE_CLIENT_SECRET,
    hasNextAuthSecret: !!process.env.NEXTAUTH_SECRET,
    clientIdFormat: process.env.GOOGLE_CLIENT_ID ? 
      (process.env.GOOGLE_CLIENT_ID.includes('.apps.googleusercontent.com') ? 'Valid format' : 'Invalid format') : 
      'Missing',
    clientSecretFormat: process.env.GOOGLE_CLIENT_SECRET ? 
      (process.env.GOOGLE_CLIENT_SECRET.startsWith('GOCSPX-') ? 'Valid format' : 'Invalid format') : 
      'Missing',
    expectedRedirectUri: 'http://localhost:3000/api/auth/callback/google',
    troubleshooting: {
      step1: 'Verify Client ID ends with .apps.googleusercontent.com',
      step2: 'Verify Client Secret starts with GOCSPX-',
      step3: 'Check redirect URI in Google Cloud Console matches exactly: http://localhost:3000/api/auth/callback/google',
      step4: 'Ensure OAuth consent screen is configured',
      step5: 'Make sure the Google project has YouTube Data API v3 enabled'
    }
  };

  return NextResponse.json(diagnostics);
}
