# YouTube Authentication Setup Guide

This guide will help you set up YouTube authentication to enable downloading of purchased content and private videos.

## 🔧 Prerequisites

- Google account
- Access to Google Cloud Console
- Basic understanding of OAuth2

## 📋 Step-by-Step Setup

### Step 1: Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Select a project" → "New Project"
3. Enter project name: "Video Downloader App"
4. Click "Create"

### Step 2: Enable YouTube Data API

1. In your project, navigate to "APIs & Services" → "Library"
2. Search for "YouTube Data API v3"
3. Click on it and then click "Enable"

### Step 3: Configure OAuth Consent Screen

1. Go to "APIs & Services" → "OAuth consent screen"
2. Choose "External" user type (unless you have Google Workspace)
3. Fill in the required information:
   - App name: "Video Downloader"
   - User support email: Your email address
   - Developer contact information: Your email address
4. Add scopes:
   - `../auth/youtube.readonly`
   - `../auth/youtube.force-ssl`
   - `../auth/youtubepartner` (if available)
5. Save and continue

### Step 4: Create OAuth2 Credentials

1. Go to "APIs & Services" → "Credentials"
2. Click "Create Credentials" → "OAuth client ID"
3. Choose "Web application"
4. Name: "Video Downloader Web Client"
5. Add Authorized redirect URIs:
   ```
   http://localhost:3000/api/auth/callback/google
   ```
6. Click "Create"
7. **Copy the Client ID and Client Secret** - you'll need these!

### Step 5: Update Environment Variables

1. Open the `.env.local` file in your project root
2. Replace the placeholder values with your actual credentials:

```env
# Replace with your actual Google Cloud Console credentials
GOOGLE_CLIENT_ID=your_actual_client_id_here.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-your_actual_client_secret_here

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=6d68319e0ac2c779871341341786d724097af209599fb8205d73ee3967a96dae

# Optional: YouTube API Key for public data
YOUTUBE_API_KEY=your_youtube_api_key_here
```

### Step 6: Restart the Development Server

After updating the environment variables:

```bash
# Stop the current server (Ctrl+C)
# Then restart it
npm run dev
```

## 🎯 Testing the Setup

1. Navigate to http://localhost:3000
2. Look for the "🔐 YouTube Authentication" section
3. Click "Sign in with Google/YouTube"
4. You should be redirected to Google's OAuth consent screen
5. Grant the requested permissions
6. You should be redirected back to your app, now authenticated

## 🔍 Troubleshooting

### "OAuth client was not found" Error
- Double-check your `GOOGLE_CLIENT_ID` in `.env.local`
- Ensure the client ID is exactly as shown in Google Cloud Console
- Make sure you've enabled the YouTube Data API v3

### "Redirect URI mismatch" Error
- Verify the redirect URI in Google Cloud Console is exactly:
  `http://localhost:3000/api/auth/callback/google`
- Check for trailing slashes or typos

### "Access denied" Error
- Make sure you've configured the OAuth consent screen
- Check that the required scopes are added
- Ensure your Google account has access to the OAuth app

### "Invalid client secret" Error
- Verify your `GOOGLE_CLIENT_SECRET` in `.env.local`
- Make sure there are no extra spaces or characters
- Regenerate the client secret if needed

## 🛡️ Security Notes

- Never commit your `.env.local` file to version control
- Keep your client secret secure and private
- Regularly rotate your credentials
- Only request the minimum required scopes

## 📚 Additional Resources

- [Google Cloud Console](https://console.cloud.google.com/)
- [YouTube Data API Documentation](https://developers.google.com/youtube/v3)
- [NextAuth.js Google Provider](https://next-auth.js.org/providers/google)
- [OAuth 2.0 Scopes for Google APIs](https://developers.google.com/identity/protocols/oauth2/scopes)

## 🎉 What You Can Do After Setup

Once authenticated, you'll be able to:

- ✅ Download your purchased YouTube movies and TV shows
- ✅ Access and download your private videos
- ✅ Download videos from your personal playlists
- ✅ Access content from your YouTube channel
- ✅ Get premium quality downloads with full metadata

## 🆘 Need Help?

If you encounter issues:

1. Check the browser console for error messages
2. Verify all environment variables are set correctly
3. Ensure the Google Cloud project is properly configured
4. Try regenerating your OAuth credentials
5. Check the server logs for detailed error information

Remember: This setup only needs to be done once. After configuration, the authentication will work seamlessly for all users of your application.
