import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { YouTubeAPIService } from '@/lib/youtube-api';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession();
    
    if (!session?.accessToken) {
      return NextResponse.json({ 
        error: 'Not authenticated' 
      }, { status: 401 });
    }

    const playlistId = params.id;
    
    if (!playlistId) {
      return NextResponse.json({ 
        error: 'Playlist ID is required' 
      }, { status: 400 });
    }

    const youtubeAPI = new YouTubeAPIService(session.accessToken as string);
    
    const videos = await youtubeAPI.getPlaylistVideos(playlistId);

    return NextResponse.json({
      success: true,
      data: videos
    });

  } catch (error: any) {
    console.error('Playlist API error:', error);
    return NextResponse.json({
      error: 'Failed to fetch playlist videos',
      details: error.message
    }, { status: 500 });
  }
}
