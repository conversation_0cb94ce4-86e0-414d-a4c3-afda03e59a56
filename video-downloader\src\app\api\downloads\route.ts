import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET(request: NextRequest) {
  try {
    const downloadsDir = path.join(process.cwd(), 'public', 'downloads');
    
    // Create downloads directory if it doesn't exist
    if (!fs.existsSync(downloadsDir)) {
      fs.mkdirSync(downloadsDir, { recursive: true });
      return NextResponse.json({ success: true, data: [] });
    }

    // Read all files in downloads directory
    const files = fs.readdirSync(downloadsDir);
    
    const downloadedFiles = files.map(filename => {
      const filePath = path.join(downloadsDir, filename);
      const stats = fs.statSync(filePath);
      
      return {
        filename: filename,
        size: stats.size,
        downloadDate: stats.mtime,
        downloadUrl: `/downloads/${filename}`
      };
    }).sort((a, b) => b.downloadDate.getTime() - a.downloadDate.getTime());

    return NextResponse.json({
      success: true,
      data: downloadedFiles
    });

  } catch (error: any) {
    console.error('Downloads list error:', error);
    return NextResponse.json({
      error: 'Failed to retrieve downloads',
      details: error.message
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { filename } = await request.json();

    if (!filename) {
      return NextResponse.json({ error: 'Filename is required' }, { status: 400 });
    }

    const downloadsDir = path.join(process.cwd(), 'public', 'downloads');
    const filePath = path.join(downloadsDir, filename);

    // Security check: ensure the file is within the downloads directory
    if (!filePath.startsWith(downloadsDir)) {
      return NextResponse.json({ error: 'Invalid file path' }, { status: 400 });
    }

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 });
    }

    // Delete the file
    fs.unlinkSync(filePath);

    return NextResponse.json({
      success: true,
      message: 'File deleted successfully'
    });

  } catch (error: any) {
    console.error('Delete file error:', error);
    return NextResponse.json({
      error: 'Failed to delete file',
      details: error.message
    }, { status: 500 });
  }
}
