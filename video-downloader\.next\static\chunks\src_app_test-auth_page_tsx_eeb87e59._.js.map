{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/app/test-auth/page.tsx"], "sourcesContent": ["'use client';\n\nimport { signIn, signOut, useSession } from 'next-auth/react';\n\nexport default function TestAuth() {\n  const { data: session, status } = useSession();\n\n  if (status === 'loading') {\n    return <div className=\"p-8\">Loading...</div>;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-100 p-8\">\n      <div className=\"max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6\">\n        <h1 className=\"text-2xl font-bold mb-6\">🔧 OAuth Test Page</h1>\n        \n        {!session ? (\n          <div className=\"space-y-4\">\n            <p className=\"text-gray-600\">Not signed in</p>\n            <button\n              onClick={() => signIn('google')}\n              className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n            >\n              Sign in with Google (Test)\n            </button>\n            \n            <div className=\"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n              <h3 className=\"font-medium text-yellow-800 mb-2\">If you get an error:</h3>\n              <ol className=\"text-sm text-yellow-700 space-y-1\">\n                <li>1. Check Google Cloud Console redirect URI is exactly: <code className=\"bg-yellow-100 px-1 rounded\">http://localhost:3000/api/auth/callback/google</code></li>\n                <li>2. Ensure OAuth consent screen is configured</li>\n                <li>3. Make sure YouTube Data API v3 is enabled</li>\n                <li>4. Check that your app is published or you're added as a test user</li>\n              </ol>\n            </div>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            <div className=\"p-4 bg-green-50 border border-green-200 rounded-lg\">\n              <h3 className=\"font-medium text-green-800\">✅ Authentication Successful!</h3>\n              <p className=\"text-sm text-green-700 mt-1\">Signed in as: {session.user?.email}</p>\n            </div>\n            \n            <div className=\"p-4 bg-gray-50 rounded-lg\">\n              <h4 className=\"font-medium mb-2\">Session Data:</h4>\n              <pre className=\"text-xs bg-gray-100 p-2 rounded overflow-auto\">\n                {JSON.stringify(session, null, 2)}\n              </pre>\n            </div>\n            \n            <button\n              onClick={() => signOut()}\n              className=\"px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700\"\n            >\n              Sign out\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAE3C,IAAI,WAAW,WAAW;QACxB,qBAAO,6LAAC;YAAI,WAAU;sBAAM;;;;;;IAC9B;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA0B;;;;;;gBAEvC,CAAC,wBACA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAC7B,6LAAC;4BACC,SAAS,IAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE;4BACtB,WAAU;sCACX;;;;;;sCAID,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;;gDAAG;8DAAuD,6LAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDACxG,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;yCAKV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAE,WAAU;;wCAA8B;wCAAe,QAAQ,IAAI,EAAE;;;;;;;;;;;;;sCAG1E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmB;;;;;;8CACjC,6LAAC;oCAAI,WAAU;8CACZ,KAAK,SAAS,CAAC,SAAS,MAAM;;;;;;;;;;;;sCAInC,6LAAC;4BACC,SAAS,IAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD;4BACrB,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAzDwB;;QACY,iJAAA,CAAA,aAAU;;;KADtB", "debugId": null}}]}