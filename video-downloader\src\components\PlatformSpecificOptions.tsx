'use client';

import { useState } from 'react';
import { SiteInfo } from '@/data/supportedSites';

interface PlatformSpecificOptionsProps {
  siteInfo: SiteInfo | null;
  onOptionsChange: (options: any) => void;
}

export default function PlatformSpecificOptions({ siteInfo, onOptionsChange }: PlatformSpecificOptionsProps) {
  const [options, setOptions] = useState<any>({});

  const updateOption = (key: string, value: any) => {
    const newOptions = { ...options, [key]: value };
    setOptions(newOptions);
    onOptionsChange(newOptions);
  };

  if (!siteInfo) {
    return null;
  }

  const renderYouTubeOptions = () => (
    <div className="space-y-4">
      <h4 className="text-sm font-medium text-gray-800 dark:text-white">
        🎥 YouTube Specific Options
      </h4>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={options.writeDescription || false}
            onChange={(e) => updateOption('writeDescription', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Save video description
          </span>
        </label>

        <label className="flex items-center">
          <input
            type="checkbox"
            checked={options.writeComments || false}
            onChange={(e) => updateOption('writeComments', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Download comments
          </span>
        </label>

        <label className="flex items-center">
          <input
            type="checkbox"
            checked={options.writeThumbnail || false}
            onChange={(e) => updateOption('writeThumbnail', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Save thumbnail
          </span>
        </label>

        <label className="flex items-center">
          <input
            type="checkbox"
            checked={options.writeAnnotations || false}
            onChange={(e) => updateOption('writeAnnotations', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Save annotations
          </span>
        </label>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Playlist Options
        </label>
        <select
          value={options.playlistItems || 'all'}
          onChange={(e) => updateOption('playlistItems', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
        >
          <option value="all">Download entire playlist</option>
          <option value="1-10">First 10 videos</option>
          <option value="1-25">First 25 videos</option>
          <option value="1-50">First 50 videos</option>
          <option value="custom">Custom range</option>
        </select>
      </div>
    </div>
  );

  const renderTikTokOptions = () => (
    <div className="space-y-4">
      <h4 className="text-sm font-medium text-gray-800 dark:text-white">
        🎵 TikTok Specific Options
      </h4>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={options.noWatermark || false}
            onChange={(e) => updateOption('noWatermark', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Remove watermark (if possible)
          </span>
        </label>

        <label className="flex items-center">
          <input
            type="checkbox"
            checked={options.downloadMusic || false}
            onChange={(e) => updateOption('downloadMusic', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Extract background music
          </span>
        </label>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          User Profile Downloads
        </label>
        <select
          value={options.userVideos || 'recent'}
          onChange={(e) => updateOption('userVideos', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
        >
          <option value="recent">Recent videos only</option>
          <option value="all">All public videos</option>
          <option value="liked">Liked videos (if accessible)</option>
        </select>
      </div>
    </div>
  );

  const renderInstagramOptions = () => (
    <div className="space-y-4">
      <h4 className="text-sm font-medium text-gray-800 dark:text-white">
        📸 Instagram Specific Options
      </h4>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={options.downloadStories || false}
            onChange={(e) => updateOption('downloadStories', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Download stories
          </span>
        </label>

        <label className="flex items-center">
          <input
            type="checkbox"
            checked={options.downloadReels || false}
            onChange={(e) => updateOption('downloadReels', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Download reels
          </span>
        </label>

        <label className="flex items-center">
          <input
            type="checkbox"
            checked={options.downloadIGTV || false}
            onChange={(e) => updateOption('downloadIGTV', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Download IGTV
          </span>
        </label>

        <label className="flex items-center">
          <input
            type="checkbox"
            checked={options.highestQuality || false}
            onChange={(e) => updateOption('highestQuality', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Highest quality available
          </span>
        </label>
      </div>
    </div>
  );

  const renderTwitchOptions = () => (
    <div className="space-y-4">
      <h4 className="text-sm font-medium text-gray-800 dark:text-white">
        🎮 Twitch Specific Options
      </h4>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={options.downloadChat || false}
            onChange={(e) => updateOption('downloadChat', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Download chat replay
          </span>
        </label>

        <label className="flex items-center">
          <input
            type="checkbox"
            checked={options.skipAds || false}
            onChange={(e) => updateOption('skipAds', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Skip advertisements
          </span>
        </label>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          VOD Quality Preference
        </label>
        <select
          value={options.twitchQuality || 'best'}
          onChange={(e) => updateOption('twitchQuality', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
        >
          <option value="best">Best available</option>
          <option value="source">Source quality</option>
          <option value="720p60">720p 60fps</option>
          <option value="720p30">720p 30fps</option>
          <option value="480p30">480p 30fps</option>
        </select>
      </div>
    </div>
  );

  const renderTwitterOptions = () => (
    <div className="space-y-4">
      <h4 className="text-sm font-medium text-gray-800 dark:text-white">
        🐦 Twitter/X Specific Options
      </h4>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={options.downloadSpaces || false}
            onChange={(e) => updateOption('downloadSpaces', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Download Spaces audio
          </span>
        </label>

        <label className="flex items-center">
          <input
            type="checkbox"
            checked={options.includeReplies || false}
            onChange={(e) => updateOption('includeReplies', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Include reply videos
          </span>
        </label>
      </div>
    </div>
  );

  const renderGenericOptions = () => (
    <div className="space-y-4">
      <h4 className="text-sm font-medium text-gray-800 dark:text-white">
        🌐 General Platform Options
      </h4>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={options.writeMetadata || false}
            onChange={(e) => updateOption('writeMetadata', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Save metadata
          </span>
        </label>

        <label className="flex items-center">
          <input
            type="checkbox"
            checked={options.embedSubs || false}
            onChange={(e) => updateOption('embedSubs', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Embed subtitles
          </span>
        </label>
      </div>
    </div>
  );

  const renderPlatformOptions = () => {
    switch (siteInfo.domain) {
      case 'youtube.com':
        return renderYouTubeOptions();
      case 'tiktok.com':
        return renderTikTokOptions();
      case 'instagram.com':
        return renderInstagramOptions();
      case 'twitch.tv':
        return renderTwitchOptions();
      case 'twitter.com':
        return renderTwitterOptions();
      default:
        return renderGenericOptions();
    }
  };

  return (
    <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
      {renderPlatformOptions()}
      
      {/* Platform-specific tips */}
      <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded">
        <h5 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
          💡 Platform Tips
        </h5>
        <div className="text-xs text-blue-700 dark:text-blue-300">
          {siteInfo.domain === 'youtube.com' && (
            <p>• Use playlist URLs to download entire playlists • Comments may take longer to process</p>
          )}
          {siteInfo.domain === 'tiktok.com' && (
            <p>• Some features may require account access • Watermark removal depends on source</p>
          )}
          {siteInfo.domain === 'instagram.com' && (
            <p>• Private accounts require authentication • Stories expire after 24 hours</p>
          )}
          {siteInfo.domain === 'twitch.tv' && (
            <p>• VODs may have limited availability • Chat replay adds significant file size</p>
          )}
          {siteInfo.domain === 'twitter.com' && (
            <p>• Some content may require authentication • Spaces are audio-only</p>
          )}
          {!['youtube.com', 'tiktok.com', 'instagram.com', 'twitch.tv', 'twitter.com'].includes(siteInfo.domain) && (
            <p>• Platform-specific features may vary • Check site documentation for details</p>
          )}
        </div>
      </div>
    </div>
  );
}
