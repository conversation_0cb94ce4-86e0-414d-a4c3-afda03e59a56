'use client';

import { useState } from 'react';
import { supportedSites, siteCategories, getTotalSitesCount, SiteInfo } from '@/data/supportedSites';

interface SiteDirectoryProps {
  onSiteSelect?: (site: SiteInfo) => void;
  onExampleSelect?: (url: string) => void;
}

export default function SiteDirectory({ onSiteSelect, onExampleSelect }: SiteDirectoryProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  const filteredSites = supportedSites.filter(site => {
    const matchesCategory = selectedCategory === 'all' || site.category === selectedCategory;
    const matchesSearch = searchTerm === '' || 
      site.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      site.domain.toLowerCase().includes(searchTerm.toLowerCase()) ||
      site.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesCategory && matchesSearch;
  });

  const handleExampleClick = (url: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (onExampleSelect) {
      onExampleSelect(url);
    }
  };

  if (!isExpanded) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
        <button
          onClick={() => setIsExpanded(true)}
          className="w-full flex items-center justify-between text-left"
        >
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
              🌐 Supported Sites Directory
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Browse {getTotalSitesCount()} supported websites and platforms
            </p>
          </div>
          <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
            🌐 Supported Sites Directory
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-300">
            {getTotalSitesCount()} supported websites across {siteCategories.length} categories
          </p>
        </div>
        <button
          onClick={() => setIsExpanded(false)}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
          </svg>
        </button>
      </div>

      {/* Search and Filter Controls */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex-1">
          <input
            type="text"
            placeholder="Search sites by name, domain, or description..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          />
        </div>
        <div>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
          >
            <option value="all">All Categories</option>
            {siteCategories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Sites Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
        {filteredSites.map((site) => (
          <div
            key={site.domain}
            className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer"
            onClick={() => onSiteSelect?.(site)}
          >
            <div className="flex items-center space-x-3 mb-3">
              <span className="text-2xl">{site.icon}</span>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-800 dark:text-white truncate">
                  {site.name}
                </h4>
                <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                  {site.domain}
                </p>
              </div>
              <span className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded">
                {site.category}
              </span>
            </div>

            <p className="text-xs text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
              {site.description}
            </p>

            {/* Features */}
            <div className="mb-3">
              <div className="flex flex-wrap gap-1">
                {site.features.slice(0, 3).map((feature) => (
                  <span
                    key={feature}
                    className="px-2 py-0.5 text-xs bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-300 rounded"
                  >
                    {feature}
                  </span>
                ))}
                {site.features.length > 3 && (
                  <span className="px-2 py-0.5 text-xs bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-300 rounded">
                    +{site.features.length - 3} more
                  </span>
                )}
              </div>
            </div>

            {/* Capabilities */}
            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-3">
              <div className="flex space-x-2">
                {site.supportsPlaylists && <span title="Supports Playlists">📋</span>}
                {site.supportsLive && <span title="Supports Live Streams">🔴</span>}
                {site.supportsSubtitles && <span title="Supports Subtitles">📝</span>}
              </div>
              <span className="font-medium">{site.maxQuality}</span>
            </div>

            {/* Example URLs */}
            {site.examples.length > 0 && (
              <div>
                <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Example URLs:
                </p>
                <div className="space-y-1">
                  {site.examples.slice(0, 2).map((example, index) => (
                    <button
                      key={index}
                      onClick={(e) => handleExampleClick(example, e)}
                      className="block w-full text-left text-xs text-blue-600 dark:text-blue-400 hover:underline truncate"
                    >
                      {example}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Notes */}
            {site.notes && (
              <div className="mt-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded">
                <p className="text-xs text-yellow-800 dark:text-yellow-200">
                  ⚠️ {site.notes}
                </p>
              </div>
            )}
          </div>
        ))}
      </div>

      {filteredSites.length === 0 && (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <p>No sites found matching your criteria</p>
          <p className="text-sm mt-1">Try adjusting your search or category filter</p>
        </div>
      )}

      {/* Footer Info */}
      <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
        <div className="flex flex-wrap items-center justify-between text-sm text-gray-600 dark:text-gray-300">
          <div>
            Showing {filteredSites.length} of {supportedSites.length} featured sites
          </div>
          <div className="flex items-center space-x-4">
            <span>📋 Playlists</span>
            <span>🔴 Live Streams</span>
            <span>📝 Subtitles</span>
          </div>
        </div>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
          💡 This is a curated list of popular sites. yt-dlp supports {getTotalSitesCount()} total sites including many regional and specialized platforms.
        </p>
      </div>
    </div>
  );
}
