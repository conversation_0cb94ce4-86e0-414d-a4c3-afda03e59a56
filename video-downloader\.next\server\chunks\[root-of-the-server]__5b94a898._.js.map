{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth';\nimport GoogleProvider from 'next-auth/providers/google';\nimport { NextAuthOptions } from 'next-auth';\n\nconst authOptions: NextAuthOptions = {\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n      authorization: {\n        params: {\n          scope: [\n            'openid',\n            'email',\n            'profile',\n            'https://www.googleapis.com/auth/youtube.readonly',\n            'https://www.googleapis.com/auth/youtube.force-ssl',\n            'https://www.googleapis.com/auth/youtubepartner'\n          ].join(' '),\n          access_type: 'offline',\n          prompt: 'consent',\n        },\n      },\n    }),\n  ],\n  callbacks: {\n    async jwt({ token, account, profile }) {\n      // Persist the OAuth access_token and refresh_token to the token right after signin\n      if (account) {\n        token.accessToken = account.access_token;\n        token.refreshToken = account.refresh_token;\n        token.accessTokenExpires = account.expires_at;\n      }\n\n      // Return previous token if the access token has not expired yet\n      if (Date.now() < (token.accessTokenExpires as number) * 1000) {\n        return token;\n      }\n\n      // Access token has expired, try to update it\n      return refreshAccessToken(token);\n    },\n    async session({ session, token }) {\n      // Send properties to the client\n      session.accessToken = token.accessToken as string;\n      session.refreshToken = token.refreshToken as string;\n      session.error = token.error as string;\n      return session;\n    },\n  },\n  session: {\n    strategy: 'jwt',\n  },\n};\n\nasync function refreshAccessToken(token: any) {\n  try {\n    const url = 'https://oauth2.googleapis.com/token';\n    \n    const response = await fetch(url, {\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n      },\n      method: 'POST',\n      body: new URLSearchParams({\n        client_id: process.env.GOOGLE_CLIENT_ID!,\n        client_secret: process.env.GOOGLE_CLIENT_SECRET!,\n        grant_type: 'refresh_token',\n        refresh_token: token.refreshToken,\n      }),\n    });\n\n    const refreshedTokens = await response.json();\n\n    if (!response.ok) {\n      throw refreshedTokens;\n    }\n\n    return {\n      ...token,\n      accessToken: refreshedTokens.access_token,\n      accessTokenExpires: Date.now() + refreshedTokens.expires_in * 1000,\n      refreshToken: refreshedTokens.refresh_token ?? token.refreshToken,\n    };\n  } catch (error) {\n    console.log(error);\n\n    return {\n      ...token,\n      error: 'RefreshAccessTokenError',\n    };\n  }\n}\n\nconst handler = NextAuth(authOptions);\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGA,MAAM,cAA+B;IACnC,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;YAC9C,eAAe;gBACb,QAAQ;oBACN,OAAO;wBACL;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD,CAAC,IAAI,CAAC;oBACP,aAAa;oBACb,QAAQ;gBACV;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE;YACnC,mFAAmF;YACnF,IAAI,SAAS;gBACX,MAAM,WAAW,GAAG,QAAQ,YAAY;gBACxC,MAAM,YAAY,GAAG,QAAQ,aAAa;gBAC1C,MAAM,kBAAkB,GAAG,QAAQ,UAAU;YAC/C;YAEA,gEAAgE;YAChE,IAAI,KAAK,GAAG,KAAK,AAAC,MAAM,kBAAkB,GAAc,MAAM;gBAC5D,OAAO;YACT;YAEA,6CAA6C;YAC7C,OAAO,mBAAmB;QAC5B;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,gCAAgC;YAChC,QAAQ,WAAW,GAAG,MAAM,WAAW;YACvC,QAAQ,YAAY,GAAG,MAAM,YAAY;YACzC,QAAQ,KAAK,GAAG,MAAM,KAAK;YAC3B,OAAO;QACT;IACF;IACA,SAAS;QACP,UAAU;IACZ;AACF;AAEA,eAAe,mBAAmB,KAAU;IAC1C,IAAI;QACF,MAAM,MAAM;QAEZ,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,SAAS;gBACP,gBAAgB;YAClB;YACA,QAAQ;YACR,MAAM,IAAI,gBAAgB;gBACxB,WAAW,QAAQ,GAAG,CAAC,gBAAgB;gBACvC,eAAe,QAAQ,GAAG,CAAC,oBAAoB;gBAC/C,YAAY;gBACZ,eAAe,MAAM,YAAY;YACnC;QACF;QAEA,MAAM,kBAAkB,MAAM,SAAS,IAAI;QAE3C,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM;QACR;QAEA,OAAO;YACL,GAAG,KAAK;YACR,aAAa,gBAAgB,YAAY;YACzC,oBAAoB,KAAK,GAAG,KAAK,gBAAgB,UAAU,GAAG;YAC9D,cAAc,gBAAgB,aAAa,IAAI,MAAM,YAAY;QACnE;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC;QAEZ,OAAO;YACL,GAAG,KAAK;YACR,OAAO;QACT;IACF;AACF;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}]}