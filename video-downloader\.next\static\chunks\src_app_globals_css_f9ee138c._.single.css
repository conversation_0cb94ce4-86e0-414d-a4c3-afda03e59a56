/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
    }
  }
}

@layer theme {
  :root, :host {
    --color-red-50: oklch(97.1% .013 17.38);
    --color-red-100: oklch(93.6% .032 17.717);
    --color-red-200: oklch(88.5% .062 18.334);
    --color-red-300: oklch(80.8% .114 19.571);
    --color-red-400: oklch(70.4% .191 22.216);
    --color-red-500: oklch(63.7% .237 25.331);
    --color-red-600: oklch(57.7% .245 27.325);
    --color-red-700: oklch(50.5% .213 27.518);
    --color-red-800: oklch(44.4% .177 26.899);
    --color-red-900: oklch(39.6% .141 25.723);
    --color-orange-50: oklch(98% .016 73.684);
    --color-orange-200: oklch(90.1% .076 70.697);
    --color-orange-400: oklch(75% .183 55.934);
    --color-orange-600: oklch(64.6% .222 41.116);
    --color-orange-800: oklch(47% .157 37.304);
    --color-orange-900: oklch(40.8% .123 38.172);
    --color-yellow-50: oklch(98.7% .026 102.212);
    --color-yellow-100: oklch(97.3% .071 103.193);
    --color-yellow-200: oklch(94.5% .129 101.54);
    --color-yellow-300: oklch(90.5% .182 98.111);
    --color-yellow-400: oklch(85.2% .199 91.936);
    --color-yellow-600: oklch(68.1% .162 75.834);
    --color-yellow-700: oklch(55.4% .135 66.442);
    --color-yellow-800: oklch(47.6% .114 61.907);
    --color-yellow-900: oklch(42.1% .095 57.708);
    --color-green-50: oklch(98.2% .018 155.826);
    --color-green-100: oklch(96.2% .044 156.743);
    --color-green-200: oklch(92.5% .084 155.995);
    --color-green-300: oklch(87.1% .15 154.449);
    --color-green-400: oklch(79.2% .209 151.711);
    --color-green-500: oklch(72.3% .219 149.579);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-green-700: oklch(52.7% .154 150.069);
    --color-green-800: oklch(44.8% .119 151.328);
    --color-green-900: oklch(39.3% .095 152.535);
    --color-blue-50: oklch(97% .014 254.604);
    --color-blue-100: oklch(93.2% .032 255.585);
    --color-blue-200: oklch(88.2% .059 254.128);
    --color-blue-300: oklch(80.9% .105 251.813);
    --color-blue-400: oklch(70.7% .165 254.624);
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-blue-700: oklch(48.8% .243 264.376);
    --color-blue-800: oklch(42.4% .199 265.638);
    --color-blue-900: oklch(37.9% .146 265.522);
    --color-indigo-100: oklch(93% .034 272.788);
    --color-purple-50: oklch(97.7% .014 308.299);
    --color-purple-100: oklch(94.6% .033 307.174);
    --color-purple-200: oklch(90.2% .063 306.703);
    --color-purple-400: oklch(71.4% .203 305.504);
    --color-purple-600: oklch(55.8% .288 302.321);
    --color-purple-700: oklch(49.6% .265 301.924);
    --color-purple-800: oklch(43.8% .218 303.724);
    --color-purple-900: oklch(38.1% .176 304.987);
    --color-gray-50: oklch(98.5% .002 247.839);
    --color-gray-100: oklch(96.7% .003 264.542);
    --color-gray-200: oklch(92.8% .006 264.531);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-400: oklch(70.7% .022 261.325);
    --color-gray-500: oklch(55.1% .027 264.364);
    --color-gray-600: oklch(44.6% .03 256.802);
    --color-gray-700: oklch(37.3% .034 259.733);
    --color-gray-800: oklch(27.8% .033 256.848);
    --color-gray-900: oklch(21% .034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-2xl: 42rem;
    --container-4xl: 56rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --animate-spin: spin 1s linear infinite;
    --aspect-video: 16 / 9;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer components;

@layer utilities {
  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .z-50 {
    z-index: 50;
  }

  .container {
    width: 100%;
  }

  @media (width >= 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (width >= 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (width >= 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (width >= 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (width >= 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .mx-auto {
    margin-inline: auto;
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-16 {
    margin-top: calc(var(--spacing) * 16);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }

  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }

  .ml-6 {
    margin-left: calc(var(--spacing) * 6);
  }

  .line-clamp-2 {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .line-clamp-3 {
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline-flex {
    display: inline-flex;
  }

  .aspect-video {
    aspect-ratio: var(--aspect-video);
  }

  .h-0\.5 {
    height: calc(var(--spacing) * .5);
  }

  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-15 {
    height: calc(var(--spacing) * 15);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-full {
    height: 100%;
  }

  .max-h-40 {
    max-height: calc(var(--spacing) * 40);
  }

  .max-h-64 {
    max-height: calc(var(--spacing) * 64);
  }

  .max-h-96 {
    max-height: calc(var(--spacing) * 96);
  }

  .max-h-\[90vh\] {
    max-height: 90vh;
  }

  .max-h-\[calc\(90vh-140px\)\] {
    max-height: calc(90vh - 140px);
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-1\.5 {
    width: calc(var(--spacing) * 1.5);
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-20 {
    width: calc(var(--spacing) * 20);
  }

  .w-full {
    width: 100%;
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .flex-1 {
    flex: 1;
  }

  .flex-shrink-0 {
    flex-shrink: 0;
  }

  .rotate-180 {
    rotate: 180deg;
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-md {
    border-radius: var(--radius-md);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }

  .border-blue-200 {
    border-color: var(--color-blue-200);
  }

  .border-blue-500 {
    border-color: var(--color-blue-500);
  }

  .border-blue-600 {
    border-color: var(--color-blue-600);
  }

  .border-gray-200 {
    border-color: var(--color-gray-200);
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-green-200 {
    border-color: var(--color-green-200);
  }

  .border-red-200 {
    border-color: var(--color-red-200);
  }

  .border-yellow-200 {
    border-color: var(--color-yellow-200);
  }

  .bg-black {
    background-color: var(--color-black);
  }

  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }

  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }

  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }

  .bg-gray-600 {
    background-color: var(--color-gray-600);
  }

  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }

  .bg-green-50 {
    background-color: var(--color-green-50);
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-green-600 {
    background-color: var(--color-green-600);
  }

  .bg-orange-50 {
    background-color: var(--color-orange-50);
  }

  .bg-purple-50 {
    background-color: var(--color-purple-50);
  }

  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }

  .bg-purple-600 {
    background-color: var(--color-purple-600);
  }

  .bg-red-50 {
    background-color: var(--color-red-50);
  }

  .bg-red-100 {
    background-color: var(--color-red-100);
  }

  .bg-red-600 {
    background-color: var(--color-red-600);
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-yellow-50 {
    background-color: var(--color-yellow-50);
  }

  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }

  .bg-yellow-600 {
    background-color: var(--color-yellow-600);
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .from-blue-50 {
    --tw-gradient-from: var(--color-blue-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-indigo-100 {
    --tw-gradient-to: var(--color-indigo-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .object-cover {
    object-fit: cover;
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .font-mono {
    font-family: var(--font-geist-mono);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-blue-700 {
    color: var(--color-blue-700);
  }

  .text-blue-800 {
    color: var(--color-blue-800);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-800 {
    color: var(--color-gray-800);
  }

  .text-green-400 {
    color: var(--color-green-400);
  }

  .text-green-600 {
    color: var(--color-green-600);
  }

  .text-green-700 {
    color: var(--color-green-700);
  }

  .text-green-800 {
    color: var(--color-green-800);
  }

  .text-orange-600 {
    color: var(--color-orange-600);
  }

  .text-orange-800 {
    color: var(--color-orange-800);
  }

  .text-purple-600 {
    color: var(--color-purple-600);
  }

  .text-purple-800 {
    color: var(--color-purple-800);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-red-700 {
    color: var(--color-red-700);
  }

  .text-red-800 {
    color: var(--color-red-800);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-yellow-600 {
    color: var(--color-yellow-600);
  }

  .text-yellow-700 {
    color: var(--color-yellow-700);
  }

  .text-yellow-800 {
    color: var(--color-yellow-800);
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  @media (hover: hover) {
    .hover\:bg-blue-700:hover {
      background-color: var(--color-blue-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-50:hover {
      background-color: var(--color-gray-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-200:hover {
      background-color: var(--color-gray-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-700:hover {
      background-color: var(--color-green-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-purple-700:hover {
      background-color: var(--color-purple-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-200:hover {
      background-color: var(--color-red-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-700:hover {
      background-color: var(--color-red-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-yellow-700:hover {
      background-color: var(--color-yellow-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-700:hover {
      color: var(--color-blue-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-600:hover {
      color: var(--color-gray-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-800:hover {
      color: var(--color-gray-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-green-700:hover {
      color: var(--color-green-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-red-700:hover {
      color: var(--color-red-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-yellow-700:hover {
      color: var(--color-yellow-700);
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      text-decoration-line: underline;
    }
  }

  .focus\:border-transparent:focus {
    border-color: #0000;
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-blue-500:focus {
    --tw-ring-color: var(--color-blue-500);
  }

  .focus\:ring-green-500:focus {
    --tw-ring-color: var(--color-green-500);
  }

  .focus\:ring-red-500:focus {
    --tw-ring-color: var(--color-red-500);
  }

  .focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  @media (width >= 48rem) {
    .md\:flex {
      display: flex;
    }
  }

  @media (width >= 48rem) {
    .md\:w-1\/3 {
      width: 33.3333%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-2\/3 {
      width: 66.6667%;
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-blue-800 {
      border-color: var(--color-blue-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-gray-600 {
      border-color: var(--color-gray-600);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-green-800 {
      border-color: var(--color-green-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-red-800 {
      border-color: var(--color-red-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-yellow-800 {
      border-color: var(--color-yellow-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-blue-800 {
      background-color: var(--color-blue-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-blue-900\/20 {
      background-color: #1c398e33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-blue-900\/20 {
        background-color: color-mix(in oklab, var(--color-blue-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-gray-600 {
      background-color: var(--color-gray-600);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-gray-700 {
      background-color: var(--color-gray-700);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-gray-800 {
      background-color: var(--color-gray-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-green-900\/20 {
      background-color: #0d542b33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-green-900\/20 {
        background-color: color-mix(in oklab, var(--color-green-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-orange-900\/20 {
      background-color: #7e2a0c33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-orange-900\/20 {
        background-color: color-mix(in oklab, var(--color-orange-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-purple-900\/20 {
      background-color: #59168b33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-purple-900\/20 {
        background-color: color-mix(in oklab, var(--color-purple-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-red-900\/20 {
      background-color: #82181a33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-red-900\/20 {
        background-color: color-mix(in oklab, var(--color-red-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-yellow-800 {
      background-color: var(--color-yellow-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-yellow-900\/20 {
      background-color: #733e0a33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-yellow-900\/20 {
        background-color: color-mix(in oklab, var(--color-yellow-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-gray-900 {
      --tw-gradient-from: var(--color-gray-900);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-gray-800 {
      --tw-gradient-to: var(--color-gray-800);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-blue-200 {
      color: var(--color-blue-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-blue-300 {
      color: var(--color-blue-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-blue-400 {
      color: var(--color-blue-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-gray-300 {
      color: var(--color-gray-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-gray-400 {
      color: var(--color-gray-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-gray-500 {
      color: var(--color-gray-500);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-green-200 {
      color: var(--color-green-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-green-300 {
      color: var(--color-green-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-green-400 {
      color: var(--color-green-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-orange-200 {
      color: var(--color-orange-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-orange-400 {
      color: var(--color-orange-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-purple-200 {
      color: var(--color-purple-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-purple-400 {
      color: var(--color-purple-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-red-200 {
      color: var(--color-red-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-red-300 {
      color: var(--color-red-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-red-400 {
      color: var(--color-red-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-white {
      color: var(--color-white);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-yellow-200 {
      color: var(--color-yellow-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-yellow-300 {
      color: var(--color-yellow-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-yellow-400 {
      color: var(--color-yellow-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-gray-600:hover {
        background-color: var(--color-gray-600);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-gray-700:hover {
        background-color: var(--color-gray-700);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-red-900\/40:hover {
        background-color: #82181a66;
      }

      @supports (color: color-mix(in lab, red, red)) {
        .dark\:hover\:bg-red-900\/40:hover {
          background-color: color-mix(in oklab, var(--color-red-900) 40%, transparent);
        }
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:text-gray-200:hover {
        color: var(--color-gray-200);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:text-white:hover {
        color: var(--color-white);
      }
    }
  }
}

:root {
  --background: #fff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/*# sourceMappingURL=src_app_globals_css_f9ee138c._.single.css.map*/