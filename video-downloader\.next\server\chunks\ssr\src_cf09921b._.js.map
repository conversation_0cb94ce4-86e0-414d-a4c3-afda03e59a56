{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/AdvancedFormatSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface Format {\n  format_id: string;\n  ext: string;\n  quality: string;\n  filesize: number;\n  format_note: string;\n  vcodec: string;\n  acodec: string;\n  fps: number;\n  resolution: string;\n  tbr?: number;\n  vbr?: number;\n  abr?: number;\n  width?: number;\n  height?: number;\n}\n\ninterface AdvancedFormatSelectorProps {\n  formats: Format[];\n  onFormatSelect: (formatId: string) => void;\n  selectedFormat: string;\n}\n\nexport default function AdvancedFormatSelector({ \n  formats, \n  onFormatSelect, \n  selectedFormat \n}: AdvancedFormatSelectorProps) {\n  const [showAdvanced, setShowAdvanced] = useState(false);\n  const [sortBy, setSortBy] = useState<'quality' | 'size' | 'format'>('quality');\n  const [filterType, setFilterType] = useState<'all' | 'video' | 'audio'>('all');\n\n  const formatFileSize = (bytes: number) => {\n    if (!bytes) return 'Unknown';\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n  };\n\n  const getQualityScore = (format: Format) => {\n    if (format.height) return format.height;\n    if (format.resolution) {\n      const match = format.resolution.match(/(\\d+)x(\\d+)/);\n      return match ? parseInt(match[2]) : 0;\n    }\n    return 0;\n  };\n\n  const getFormatType = (format: Format) => {\n    const hasVideo = format.vcodec && format.vcodec !== 'none';\n    const hasAudio = format.acodec && format.acodec !== 'none';\n    \n    if (hasVideo && hasAudio) return 'video+audio';\n    if (hasVideo) return 'video';\n    if (hasAudio) return 'audio';\n    return 'unknown';\n  };\n\n  const getCodecInfo = (format: Format) => {\n    const video = format.vcodec && format.vcodec !== 'none' ? format.vcodec : null;\n    const audio = format.acodec && format.acodec !== 'none' ? format.acodec : null;\n    \n    if (video && audio) return `${video} + ${audio}`;\n    if (video) return video;\n    if (audio) return audio;\n    return 'Unknown';\n  };\n\n  const getBitrateInfo = (format: Format) => {\n    const parts = [];\n    if (format.vbr) parts.push(`V: ${format.vbr}k`);\n    if (format.abr) parts.push(`A: ${format.abr}k`);\n    if (format.tbr && !format.vbr && !format.abr) parts.push(`${format.tbr}k`);\n    return parts.length > 0 ? parts.join(', ') : 'Unknown';\n  };\n\n  const filteredFormats = formats.filter(format => {\n    if (filterType === 'all') return true;\n    const type = getFormatType(format);\n    if (filterType === 'video') return type === 'video+audio' || type === 'video';\n    if (filterType === 'audio') return type === 'audio';\n    return true;\n  });\n\n  const sortedFormats = [...filteredFormats].sort((a, b) => {\n    switch (sortBy) {\n      case 'quality':\n        return getQualityScore(b) - getQualityScore(a);\n      case 'size':\n        return (b.filesize || 0) - (a.filesize || 0);\n      case 'format':\n        return a.ext.localeCompare(b.ext);\n      default:\n        return 0;\n    }\n  });\n\n  if (!showAdvanced) {\n    return (\n      <div className=\"mt-2\">\n        <button\n          onClick={() => setShowAdvanced(true)}\n          className=\"text-sm text-blue-600 dark:text-blue-400 hover:underline\"\n        >\n          🔧 Advanced Format Selection ({formats.length} formats available)\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"mt-4 border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h4 className=\"text-sm font-medium text-gray-800 dark:text-white\">\n          🔧 Advanced Format Selection\n        </h4>\n        <button\n          onClick={() => setShowAdvanced(false)}\n          className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-200\"\n        >\n          ✕\n        </button>\n      </div>\n\n      {/* Controls */}\n      <div className=\"flex flex-wrap gap-4 mb-4\">\n        <div>\n          <label className=\"block text-xs text-gray-600 dark:text-gray-400 mb-1\">\n            Filter by Type\n          </label>\n          <select\n            value={filterType}\n            onChange={(e) => setFilterType(e.target.value as any)}\n            className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white\"\n          >\n            <option value=\"all\">All Formats</option>\n            <option value=\"video\">Video + Audio</option>\n            <option value=\"audio\">Audio Only</option>\n          </select>\n        </div>\n\n        <div>\n          <label className=\"block text-xs text-gray-600 dark:text-gray-400 mb-1\">\n            Sort by\n          </label>\n          <select\n            value={sortBy}\n            onChange={(e) => setSortBy(e.target.value as any)}\n            className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white\"\n          >\n            <option value=\"quality\">Quality</option>\n            <option value=\"size\">File Size</option>\n            <option value=\"format\">Format</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Format List */}\n      <div className=\"max-h-64 overflow-y-auto\">\n        <div className=\"space-y-2\">\n          {sortedFormats.slice(0, 20).map((format) => (\n            <label\n              key={format.format_id}\n              className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${\n                selectedFormat === format.format_id\n                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                  : 'border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'\n              }`}\n            >\n              <input\n                type=\"radio\"\n                name=\"advancedFormat\"\n                value={format.format_id}\n                checked={selectedFormat === format.format_id}\n                onChange={() => onFormatSelect(format.format_id)}\n                className=\"mr-3\"\n              />\n              \n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center justify-between mb-1\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"text-sm font-medium text-gray-800 dark:text-white\">\n                      {format.resolution || 'Audio Only'}\n                    </span>\n                    <span className=\"px-2 py-0.5 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded\">\n                      {format.ext.toUpperCase()}\n                    </span>\n                    <span className={`px-2 py-0.5 text-xs rounded ${\n                      getFormatType(format) === 'video+audio' \n                        ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200'\n                        : getFormatType(format) === 'video'\n                        ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200'\n                        : 'bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-200'\n                    }`}>\n                      {getFormatType(format) === 'video+audio' ? '🎥+🎵' : \n                       getFormatType(format) === 'video' ? '🎥' : '🎵'}\n                    </span>\n                  </div>\n                  <span className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    {formatFileSize(format.filesize)}\n                  </span>\n                </div>\n                \n                <div className=\"grid grid-cols-2 gap-4 text-xs text-gray-500 dark:text-gray-400\">\n                  <div>\n                    <span className=\"font-medium\">Codec:</span> {getCodecInfo(format)}\n                  </div>\n                  <div>\n                    <span className=\"font-medium\">Bitrate:</span> {getBitrateInfo(format)}\n                  </div>\n                  {format.fps && (\n                    <div>\n                      <span className=\"font-medium\">FPS:</span> {format.fps}\n                    </div>\n                  )}\n                  {format.format_note && (\n                    <div>\n                      <span className=\"font-medium\">Note:</span> {format.format_note}\n                    </div>\n                  )}\n                </div>\n              </div>\n            </label>\n          ))}\n        </div>\n      </div>\n\n      {sortedFormats.length > 20 && (\n        <div className=\"mt-2 text-xs text-gray-500 dark:text-gray-400 text-center\">\n          Showing top 20 of {sortedFormats.length} formats\n        </div>\n      )}\n\n      {sortedFormats.length === 0 && (\n        <div className=\"text-center py-4 text-gray-500 dark:text-gray-400\">\n          No formats match the current filter\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AA2Be,SAAS,uBAAuB,EAC7C,OAAO,EACP,cAAc,EACd,cAAc,EACc;IAC5B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiC;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IAExE,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,OAAO,OAAO;QACnB,MAAM,QAAQ;YAAC;YAAK;YAAM;YAAM;SAAK;QACrC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,CAAC,EAAE;IAC3E;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,OAAO,MAAM,EAAE,OAAO,OAAO,MAAM;QACvC,IAAI,OAAO,UAAU,EAAE;YACrB,MAAM,QAAQ,OAAO,UAAU,CAAC,KAAK,CAAC;YACtC,OAAO,QAAQ,SAAS,KAAK,CAAC,EAAE,IAAI;QACtC;QACA,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,WAAW,OAAO,MAAM,IAAI,OAAO,MAAM,KAAK;QACpD,MAAM,WAAW,OAAO,MAAM,IAAI,OAAO,MAAM,KAAK;QAEpD,IAAI,YAAY,UAAU,OAAO;QACjC,IAAI,UAAU,OAAO;QACrB,IAAI,UAAU,OAAO;QACrB,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ,OAAO,MAAM,IAAI,OAAO,MAAM,KAAK,SAAS,OAAO,MAAM,GAAG;QAC1E,MAAM,QAAQ,OAAO,MAAM,IAAI,OAAO,MAAM,KAAK,SAAS,OAAO,MAAM,GAAG;QAE1E,IAAI,SAAS,OAAO,OAAO,GAAG,MAAM,GAAG,EAAE,OAAO;QAChD,IAAI,OAAO,OAAO;QAClB,IAAI,OAAO,OAAO;QAClB,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,QAAQ,EAAE;QAChB,IAAI,OAAO,GAAG,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;QAC9C,IAAI,OAAO,GAAG,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;QAC9C,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,EAAE,MAAM,IAAI,CAAC,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC;QACzE,OAAO,MAAM,MAAM,GAAG,IAAI,MAAM,IAAI,CAAC,QAAQ;IAC/C;IAEA,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA;QACrC,IAAI,eAAe,OAAO,OAAO;QACjC,MAAM,OAAO,cAAc;QAC3B,IAAI,eAAe,SAAS,OAAO,SAAS,iBAAiB,SAAS;QACtE,IAAI,eAAe,SAAS,OAAO,SAAS;QAC5C,OAAO;IACT;IAEA,MAAM,gBAAgB;WAAI;KAAgB,CAAC,IAAI,CAAC,CAAC,GAAG;QAClD,OAAQ;YACN,KAAK;gBACH,OAAO,gBAAgB,KAAK,gBAAgB;YAC9C,KAAK;gBACH,OAAO,CAAC,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,IAAI,CAAC;YAC7C,KAAK;gBACH,OAAO,EAAE,GAAG,CAAC,aAAa,CAAC,EAAE,GAAG;YAClC;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,cAAc;QACjB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBACC,SAAS,IAAM,gBAAgB;gBAC/B,WAAU;;oBACX;oBACgC,QAAQ,MAAM;oBAAC;;;;;;;;;;;;IAItD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoD;;;;;;kCAGlE,8OAAC;wBACC,SAAS,IAAM,gBAAgB;wBAC/B,WAAU;kCACX;;;;;;;;;;;;0BAMH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAsD;;;;;;0CAGvE,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;;;;;;;;;;;;;kCAI1B,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAsD;;;;;;0CAGvE,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gCACzC,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,cAAc,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,uBAC/B,8OAAC;4BAEC,WAAW,CAAC,yEAAyE,EACnF,mBAAmB,OAAO,SAAS,GAC/B,mDACA,gFACJ;;8CAEF,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,OAAO,SAAS;oCACvB,SAAS,mBAAmB,OAAO,SAAS;oCAC5C,UAAU,IAAM,eAAe,OAAO,SAAS;oCAC/C,WAAU;;;;;;8CAGZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,OAAO,UAAU,IAAI;;;;;;sEAExB,8OAAC;4DAAK,WAAU;sEACb,OAAO,GAAG,CAAC,WAAW;;;;;;sEAEzB,8OAAC;4DAAK,WAAW,CAAC,4BAA4B,EAC5C,cAAc,YAAY,gBACtB,yEACA,cAAc,YAAY,UAC1B,qEACA,4EACJ;sEACC,cAAc,YAAY,gBAAgB,UAC1C,cAAc,YAAY,UAAU,OAAO;;;;;;;;;;;;8DAGhD,8OAAC;oDAAK,WAAU;8DACb,eAAe,OAAO,QAAQ;;;;;;;;;;;;sDAInC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAa;wDAAE,aAAa;;;;;;;8DAE5D,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAe;wDAAE,eAAe;;;;;;;gDAE/D,OAAO,GAAG,kBACT,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAW;wDAAE,OAAO,GAAG;;;;;;;gDAGxD,OAAO,WAAW,kBACjB,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAY;wDAAE,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;2BAvDjE,OAAO,SAAS;;;;;;;;;;;;;;;YAiE5B,cAAc,MAAM,GAAG,oBACtB,8OAAC;gBAAI,WAAU;;oBAA4D;oBACtD,cAAc,MAAM;oBAAC;;;;;;;YAI3C,cAAc,MAAM,KAAK,mBACxB,8OAAC;gBAAI,WAAU;0BAAoD;;;;;;;;;;;;AAM3E", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/VideoInfoCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Image from 'next/image';\nimport AdvancedFormatSelector from './AdvancedFormatSelector';\n\ninterface VideoInfo {\n  title: string;\n  description: string;\n  duration: number;\n  uploader: string;\n  upload_date: string;\n  view_count: number;\n  thumbnail: string;\n  formats: Array<{\n    format_id: string;\n    ext: string;\n    quality: string;\n    filesize: number;\n    format_note: string;\n    vcodec: string;\n    acodec: string;\n    fps: number;\n    resolution: string;\n  }>;\n  url: string;\n  webpage_url: string;\n  extractor: string;\n}\n\ninterface VideoInfoCardProps {\n  videoInfo: VideoInfo;\n  onDownload: (format?: string, quality?: string, type?: string, audioFormat?: string, subtitleOptions?: any) => void;\n}\n\nexport default function VideoInfoCard({ videoInfo, onDownload }: VideoInfoCardProps) {\n  const [selectedFormat, setSelectedFormat] = useState('best');\n  const [selectedQuality, setSelectedQuality] = useState('');\n  const [downloadType, setDownloadType] = useState<'video' | 'audio'>('video');\n  const [audioFormat, setAudioFormat] = useState('mp3');\n  const [includeSubtitles, setIncludeSubtitles] = useState(false);\n  const [subtitleLanguages, setSubtitleLanguages] = useState('en');\n\n  const formatDuration = (seconds: number) => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const formatDate = (dateString: string) => {\n    if (!dateString) return 'Unknown';\n    const year = dateString.substring(0, 4);\n    const month = dateString.substring(4, 6);\n    const day = dateString.substring(6, 8);\n    return `${year}-${month}-${day}`;\n  };\n\n  const formatViewCount = (count: number) => {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  };\n\n  const handleDownload = () => {\n    const subtitleOptions = includeSubtitles ? {\n      includeSubtitles: true,\n      subtitleLanguages: subtitleLanguages\n    } : undefined;\n\n    if (downloadType === 'audio') {\n      onDownload(`bestaudio[ext=${audioFormat}]/bestaudio`, undefined, 'audio', audioFormat, subtitleOptions);\n    } else if (selectedFormat === 'best') {\n      onDownload(undefined, undefined, undefined, undefined, subtitleOptions);\n    } else if (selectedFormat === 'quality' && selectedQuality) {\n      onDownload(undefined, selectedQuality, undefined, undefined, subtitleOptions);\n    } else {\n      onDownload(selectedFormat, undefined, undefined, undefined, subtitleOptions);\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden\">\n      <div className=\"md:flex\">\n        <div className=\"md:w-1/3\">\n          {videoInfo.thumbnail && (\n            <div className=\"relative aspect-video\">\n              <Image\n                src={videoInfo.thumbnail}\n                alt={videoInfo.title}\n                fill\n                className=\"object-cover\"\n                unoptimized\n              />\n            </div>\n          )}\n        </div>\n\n        <div className=\"md:w-2/3 p-6\">\n          <h2 className=\"text-xl font-bold text-gray-800 dark:text-white mb-2\">\n            {videoInfo.title}\n          </h2>\n          \n          <div className=\"grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-300 mb-4\">\n            <div>\n              <span className=\"font-medium\">Uploader:</span> {videoInfo.uploader}\n            </div>\n            <div>\n              <span className=\"font-medium\">Duration:</span> {formatDuration(videoInfo.duration)}\n            </div>\n            <div>\n              <span className=\"font-medium\">Views:</span> {formatViewCount(videoInfo.view_count)}\n            </div>\n            <div>\n              <span className=\"font-medium\">Upload Date:</span> {formatDate(videoInfo.upload_date)}\n            </div>\n            <div>\n              <span className=\"font-medium\">Source:</span> {videoInfo.extractor}\n            </div>\n          </div>\n\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Download Type\n              </label>\n              <div className=\"flex space-x-4 mb-4\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    name=\"downloadType\"\n                    value=\"video\"\n                    checked={downloadType === 'video'}\n                    onChange={(e) => setDownloadType(e.target.value as 'video' | 'audio')}\n                    className=\"mr-2\"\n                  />\n                  <span className=\"text-sm\">🎥 Video</span>\n                </label>\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    name=\"downloadType\"\n                    value=\"audio\"\n                    checked={downloadType === 'audio'}\n                    onChange={(e) => setDownloadType(e.target.value as 'video' | 'audio')}\n                    className=\"mr-2\"\n                  />\n                  <span className=\"text-sm\">🎵 Audio Only</span>\n                </label>\n              </div>\n            </div>\n\n            {downloadType === 'audio' && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Audio Format\n                </label>\n                <select\n                  value={audioFormat}\n                  onChange={(e) => setAudioFormat(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white\"\n                >\n                  <option value=\"mp3\">MP3 (Most Compatible)</option>\n                  <option value=\"m4a\">M4A (High Quality)</option>\n                  <option value=\"wav\">WAV (Uncompressed)</option>\n                  <option value=\"flac\">FLAC (Lossless)</option>\n                  <option value=\"ogg\">OGG (Open Source)</option>\n                </select>\n              </div>\n            )}\n\n            <div>\n              <label className=\"flex items-center mb-2\">\n                <input\n                  type=\"checkbox\"\n                  checked={includeSubtitles}\n                  onChange={(e) => setIncludeSubtitles(e.target.checked)}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  📝 Download Subtitles/Captions\n                </span>\n              </label>\n              \n              {includeSubtitles && (\n                <div className=\"ml-6\">\n                  <input\n                    type=\"text\"\n                    value={subtitleLanguages}\n                    onChange={(e) => setSubtitleLanguages(e.target.value)}\n                    placeholder=\"en,es,fr\"\n                    className=\"w-full px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white\"\n                  />\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                    Language codes (comma-separated): en, es, fr, de, ja\n                  </p>\n                </div>\n              )}\n            </div>\n\n            {downloadType === 'video' && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Video Quality Options\n                </label>\n                \n                <div className=\"space-y-2\">\n                  <label className=\"flex items-center\">\n                    <input\n                      type=\"radio\"\n                      name=\"format\"\n                      value=\"best\"\n                      checked={selectedFormat === 'best'}\n                      onChange={(e) => setSelectedFormat(e.target.value)}\n                      className=\"mr-2\"\n                    />\n                    <span className=\"text-sm\">Best Quality (Automatic)</span>\n                  </label>\n\n                  <label className=\"flex items-center\">\n                    <input\n                      type=\"radio\"\n                      name=\"format\"\n                      value=\"quality\"\n                      checked={selectedFormat === 'quality'}\n                      onChange={(e) => setSelectedFormat(e.target.value)}\n                      className=\"mr-2\"\n                    />\n                    <span className=\"text-sm\">Select Quality:</span>\n                    <select\n                      value={selectedQuality}\n                      onChange={(e) => {\n                        setSelectedQuality(e.target.value);\n                        setSelectedFormat('quality');\n                      }}\n                      className=\"ml-2 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm dark:bg-gray-700 dark:text-white\"\n                      disabled={selectedFormat !== 'quality'}\n                    >\n                      <option value=\"\">Select...</option>\n                      <option value=\"720\">720p</option>\n                      <option value=\"480\">480p</option>\n                      <option value=\"360\">360p</option>\n                      <option value=\"240\">240p</option>\n                    </select>\n                  </label>\n                </div>\n\n                <AdvancedFormatSelector\n                  formats={videoInfo.formats}\n                  onFormatSelect={setSelectedFormat}\n                  selectedFormat={selectedFormat}\n                />\n              </div>\n            )}\n\n            <button\n              onClick={handleDownload}\n              className=\"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors\"\n            >\n              {downloadType === 'audio' ? `Extract Audio (${audioFormat.toUpperCase()})` : 'Download Video'}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAmCe,SAAS,cAAc,EAAE,SAAS,EAAE,UAAU,EAAsB;IACjF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,iBAAiB,CAAC;QACtB,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;QAC9C,MAAM,OAAO,UAAU;QAEvB,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;QAC9F;QACA,OAAO,GAAG,QAAQ,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACzD;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,MAAM,OAAO,WAAW,SAAS,CAAC,GAAG;QACrC,MAAM,QAAQ,WAAW,SAAS,CAAC,GAAG;QACtC,MAAM,MAAM,WAAW,SAAS,CAAC,GAAG;QACpC,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK;IAClC;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,SAAS,SAAS;YACpB,OAAO,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,KAAK;QACxC,OAAO,IAAI,SAAS,MAAM;YACxB,OAAO,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,KAAK;QACrC;QACA,OAAO,MAAM,QAAQ;IACvB;IAEA,MAAM,iBAAiB;QACrB,MAAM,kBAAkB,mBAAmB;YACzC,kBAAkB;YAClB,mBAAmB;QACrB,IAAI;QAEJ,IAAI,iBAAiB,SAAS;YAC5B,WAAW,CAAC,cAAc,EAAE,YAAY,WAAW,CAAC,EAAE,WAAW,SAAS,aAAa;QACzF,OAAO,IAAI,mBAAmB,QAAQ;YACpC,WAAW,WAAW,WAAW,WAAW,WAAW;QACzD,OAAO,IAAI,mBAAmB,aAAa,iBAAiB;YAC1D,WAAW,WAAW,iBAAiB,WAAW,WAAW;QAC/D,OAAO;YACL,WAAW,gBAAgB,WAAW,WAAW,WAAW;QAC9D;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACZ,UAAU,SAAS,kBAClB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,UAAU,SAAS;4BACxB,KAAK,UAAU,KAAK;4BACpB,IAAI;4BACJ,WAAU;4BACV,WAAW;;;;;;;;;;;;;;;;8BAMnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,UAAU,KAAK;;;;;;sCAGlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAgB;wCAAE,UAAU,QAAQ;;;;;;;8CAEpE,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAgB;wCAAE,eAAe,UAAU,QAAQ;;;;;;;8CAEnF,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAa;wCAAE,gBAAgB,UAAU,UAAU;;;;;;;8CAEnF,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAmB;wCAAE,WAAW,UAAU,WAAW;;;;;;;8CAErF,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAc;wCAAE,UAAU,SAAS;;;;;;;;;;;;;sCAIrE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,iBAAiB;4DAC1B,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAC/C,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,iBAAiB;4DAC1B,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAC/C,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;gCAK/B,iBAAiB,yBAChB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;;;;;;;;;;;;;8CAK1B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,OAAO;oDACrD,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAAuD;;;;;;;;;;;;wCAKxE,kCACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDACpD,aAAY;oDACZ,WAAU;;;;;;8DAEZ,8OAAC;oDAAE,WAAU;8DAAgD;;;;;;;;;;;;;;;;;;gCAOlE,iBAAiB,yBAChB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAInF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,mBAAmB;4DAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4DACjD,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAG5B,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,mBAAmB;4DAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4DACjD,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC;4DACC,OAAO;4DACP,UAAU,CAAC;gEACT,mBAAmB,EAAE,MAAM,CAAC,KAAK;gEACjC,kBAAkB;4DACpB;4DACA,WAAU;4DACV,UAAU,mBAAmB;;8EAE7B,8OAAC;oEAAO,OAAM;8EAAG;;;;;;8EACjB,8OAAC;oEAAO,OAAM;8EAAM;;;;;;8EACpB,8OAAC;oEAAO,OAAM;8EAAM;;;;;;8EACpB,8OAAC;oEAAO,OAAM;8EAAM;;;;;;8EACpB,8OAAC;oEAAO,OAAM;8EAAM;;;;;;;;;;;;;;;;;;;;;;;;sDAK1B,8OAAC,4IAAA,CAAA,UAAsB;4CACrB,SAAS,UAAU,OAAO;4CAC1B,gBAAgB;4CAChB,gBAAgB;;;;;;;;;;;;8CAKtB,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAET,iBAAiB,UAAU,CAAC,eAAe,EAAE,YAAY,WAAW,GAAG,CAAC,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3F", "debugId": null}}, {"offset": {"line": 1052, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/DownloadProgress.tsx"], "sourcesContent": ["'use client';\n\ninterface DownloadStatus {\n  id: string;\n  status: 'downloading' | 'completed' | 'failed';\n  progress: number;\n  filename: string | null;\n  url: string;\n  startTime: number;\n  error: string | null;\n}\n\ninterface DownloadProgressProps {\n  download: DownloadStatus;\n}\n\nexport default function DownloadProgress({ download }: DownloadProgressProps) {\n  const getStatusColor = () => {\n    switch (download.status) {\n      case 'downloading':\n        return 'bg-blue-600';\n      case 'completed':\n        return 'bg-green-600';\n      case 'failed':\n        return 'bg-red-600';\n      default:\n        return 'bg-gray-600';\n    }\n  };\n\n  const getStatusText = () => {\n    switch (download.status) {\n      case 'downloading':\n        return 'Downloading...';\n      case 'completed':\n        return 'Completed';\n      case 'failed':\n        return 'Failed';\n      default:\n        return 'Unknown';\n    }\n  };\n\n  const formatElapsedTime = () => {\n    const elapsed = Date.now() - download.startTime;\n    const seconds = Math.floor(elapsed / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const hours = Math.floor(minutes / 60);\n\n    if (hours > 0) {\n      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;\n    } else if (minutes > 0) {\n      return `${minutes}m ${seconds % 60}s`;\n    } else {\n      return `${seconds}s`;\n    }\n  };\n\n  const getDisplayUrl = () => {\n    try {\n      const url = new URL(download.url);\n      return url.hostname;\n    } catch {\n      return download.url.substring(0, 50) + '...';\n    }\n  };\n\n  return (\n    <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n      <div className=\"flex items-center justify-between mb-2\">\n        <div className=\"flex-1 min-w-0\">\n          <p className=\"text-sm font-medium text-gray-800 dark:text-white truncate\">\n            {download.filename || 'Preparing download...'}\n          </p>\n          <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\n            {getDisplayUrl()}\n          </p>\n        </div>\n        <div className=\"ml-4 flex items-center space-x-2\">\n          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white ${getStatusColor()}`}>\n            {getStatusText()}\n          </span>\n          <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n            {formatElapsedTime()}\n          </span>\n        </div>\n      </div>\n\n      {/* Progress Bar */}\n      <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2\">\n        <div\n          className={`h-2 rounded-full transition-all duration-300 ${getStatusColor()}`}\n          style={{ width: `${Math.max(0, Math.min(100, download.progress))}%` }}\n        ></div>\n      </div>\n\n      <div className=\"flex justify-between items-center\">\n        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n          {download.progress.toFixed(1)}%\n        </span>\n        {download.status === 'completed' && download.filename && (\n          <a\n            href={`/downloads/${download.filename}`}\n            download\n            className=\"text-xs text-blue-600 dark:text-blue-400 hover:underline\"\n          >\n            Download File\n          </a>\n        )}\n      </div>\n\n      {download.error && (\n        <div className=\"mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded\">\n          <p className=\"text-xs text-red-800 dark:text-red-200\">\n            Error: {download.error}\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAgBe,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;IAC1E,MAAM,iBAAiB;QACrB,OAAQ,SAAS,MAAM;YACrB,KAAK;gBACH,OAAO;YACT,KAAK;gBA<PERSON>,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ,SAAS,MAAM;YACrB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,UAAU,KAAK,GAAG,KAAK,SAAS,SAAS;QAC/C,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QAEnC,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,EAAE,EAAE,UAAU,GAAG,EAAE,EAAE,UAAU,GAAG,CAAC,CAAC;QACtD,OAAO,IAAI,UAAU,GAAG;YACtB,OAAO,GAAG,QAAQ,EAAE,EAAE,UAAU,GAAG,CAAC,CAAC;QACvC,OAAO;YACL,OAAO,GAAG,QAAQ,CAAC,CAAC;QACtB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,MAAM,IAAI,IAAI,SAAS,GAAG;YAChC,OAAO,IAAI,QAAQ;QACrB,EAAE,OAAM;YACN,OAAO,SAAS,GAAG,CAAC,SAAS,CAAC,GAAG,MAAM;QACzC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CACV,SAAS,QAAQ,IAAI;;;;;;0CAExB,8OAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;kCAGL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAW,CAAC,mFAAmF,EAAE,kBAAkB;0CACtH;;;;;;0CAEH,8OAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;;;;;;;0BAMP,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAW,CAAC,6CAA6C,EAAE,kBAAkB;oBAC7E,OAAO;wBAAE,OAAO,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,SAAS,QAAQ,GAAG,CAAC,CAAC;oBAAC;;;;;;;;;;;0BAIxE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;;4BACb,SAAS,QAAQ,CAAC,OAAO,CAAC;4BAAG;;;;;;;oBAE/B,SAAS,MAAM,KAAK,eAAe,SAAS,QAAQ,kBACnD,8OAAC;wBACC,MAAM,CAAC,WAAW,EAAE,SAAS,QAAQ,EAAE;wBACvC,QAAQ;wBACR,WAAU;kCACX;;;;;;;;;;;;YAMJ,SAAS,KAAK,kBACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;wBAAyC;wBAC5C,SAAS,KAAK;;;;;;;;;;;;;;;;;;AAMlC", "debugId": null}}, {"offset": {"line": 1244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/DownloadHistory.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface DownloadFile {\n  filename: string;\n  size: number;\n  downloadDate: string;\n  downloadUrl: string;\n}\n\ninterface DownloadHistoryProps {\n  downloads: DownloadFile[];\n  onDelete: (filename: string) => void;\n  onRefresh: () => void;\n}\n\nexport default function DownloadHistory({ downloads, onDelete, onRefresh }: DownloadHistoryProps) {\n  const [deletingFiles, setDeletingFiles] = useState<Set<string>>(new Set());\n\n  const formatFileSize = (bytes: number) => {\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n  };\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();\n  };\n\n  const handleDelete = async (filename: string) => {\n    if (confirm(`Are you sure you want to delete \"${filename}\"?`)) {\n      setDeletingFiles(prev => new Set(prev).add(filename));\n      try {\n        await onDelete(filename);\n      } finally {\n        setDeletingFiles(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(filename);\n          return newSet;\n        });\n      }\n    }\n  };\n\n  const getFileExtension = (filename: string) => {\n    return filename.split('.').pop()?.toLowerCase() || '';\n  };\n\n  const getFileIcon = (filename: string) => {\n    const ext = getFileExtension(filename);\n    switch (ext) {\n      case 'mp4':\n      case 'avi':\n      case 'mov':\n      case 'mkv':\n      case 'webm':\n        return '🎥';\n      case 'mp3':\n      case 'wav':\n      case 'flac':\n      case 'm4a':\n        return '🎵';\n      default:\n        return '📄';\n    }\n  };\n\n  if (downloads.length === 0) {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white\">\n            Download History\n          </h3>\n          <button\n            onClick={onRefresh}\n            className=\"px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n          >\n            Refresh\n          </button>\n        </div>\n        <div className=\"text-center py-8\">\n          <p className=\"text-gray-500 dark:text-gray-400\">No downloads yet</p>\n          <p className=\"text-sm text-gray-400 dark:text-gray-500 mt-1\">\n            Downloaded files will appear here\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white\">\n          Download History ({downloads.length})\n        </h3>\n        <button\n          onClick={onRefresh}\n          className=\"px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n        >\n          Refresh\n        </button>\n      </div>\n\n      <div className=\"space-y-3\">\n        {downloads.map((download) => (\n          <div\n            key={download.filename}\n            className=\"flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <div className=\"flex items-center space-x-3 flex-1 min-w-0\">\n              <span className=\"text-2xl\">{getFileIcon(download.filename)}</span>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-800 dark:text-white truncate\">\n                  {download.filename}\n                </p>\n                <div className=\"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400\">\n                  <span>{formatFileSize(download.size)}</span>\n                  <span>{formatDate(download.downloadDate)}</span>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-2 ml-4\">\n              <a\n                href={download.downloadUrl}\n                download\n                className=\"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\"\n              >\n                Download\n              </a>\n              <button\n                onClick={() => handleDelete(download.filename)}\n                disabled={deletingFiles.has(download.filename)}\n                className=\"px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                {deletingFiles.has(download.filename) ? 'Deleting...' : 'Delete'}\n              </button>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {downloads.length > 5 && (\n        <div className=\"mt-4 text-center\">\n          <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n            Showing {downloads.length} downloads\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAiBe,SAAS,gBAAgB,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAwB;IAC9F,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAEpE,MAAM,iBAAiB,CAAC;QACtB,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,CAAC,EAAE;IAC3E;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,KAAK,MAAM,KAAK,kBAAkB;IAClE;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,QAAQ,CAAC,iCAAiC,EAAE,SAAS,EAAE,CAAC,GAAG;YAC7D,iBAAiB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC;YAC3C,IAAI;gBACF,MAAM,SAAS;YACjB,SAAU;gBACR,iBAAiB,CAAA;oBACf,MAAM,SAAS,IAAI,IAAI;oBACvB,OAAO,MAAM,CAAC;oBACd,OAAO;gBACT;YACF;QACF;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI,iBAAiB;IACrD;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,MAAM,iBAAiB;QAC7B,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAIH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;sCAChD,8OAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;;;;;;;IAMrE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAsD;4BAC/C,UAAU,MAAM;4BAAC;;;;;;;kCAEtC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAKH,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;wBAEC,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAY,YAAY,SAAS,QAAQ;;;;;;kDACzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DACV,SAAS,QAAQ;;;;;;0DAEpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAM,eAAe,SAAS,IAAI;;;;;;kEACnC,8OAAC;kEAAM,WAAW,SAAS,YAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAK7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAM,SAAS,WAAW;wCAC1B,QAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,aAAa,SAAS,QAAQ;wCAC7C,UAAU,cAAc,GAAG,CAAC,SAAS,QAAQ;wCAC7C,WAAU;kDAET,cAAc,GAAG,CAAC,SAAS,QAAQ,IAAI,gBAAgB;;;;;;;;;;;;;uBA7BvD,SAAS,QAAQ;;;;;;;;;;YAoC3B,UAAU,MAAM,GAAG,mBAClB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;wBAA2C;wBAC7C,UAAU,MAAM;wBAAC;;;;;;;;;;;;;;;;;;AAMtC", "debugId": null}}, {"offset": {"line": 1532, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/BatchDownloader.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface BatchDownloadProps {\n  onBatchDownload: (urls: string[], options: BatchDownloadOptions) => void;\n}\n\ninterface BatchDownloadOptions {\n  type: 'video' | 'audio';\n  audioFormat?: string;\n  quality?: string;\n  format?: string;\n}\n\nexport default function BatchDownloader({ onBatchDownload }: BatchDownloadProps) {\n  const [urlText, setUrlText] = useState('');\n  const [downloadType, setDownloadType] = useState<'video' | 'audio'>('video');\n  const [audioFormat, setAudioFormat] = useState('mp3');\n  const [quality, setQuality] = useState('');\n  const [isExpanded, setIsExpanded] = useState(false);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    // Parse URLs from text (one per line)\n    const urls = urlText\n      .split('\\n')\n      .map(url => url.trim())\n      .filter(url => url.length > 0 && isValidUrl(url));\n\n    if (urls.length === 0) {\n      alert('Please enter at least one valid URL');\n      return;\n    }\n\n    const options: BatchDownloadOptions = {\n      type: downloadType,\n      audioFormat: downloadType === 'audio' ? audioFormat : undefined,\n      quality: downloadType === 'video' ? quality : undefined,\n    };\n\n    onBatchDownload(urls, options);\n    setUrlText(''); // Clear the text area after submission\n  };\n\n  const isValidUrl = (string: string) => {\n    try {\n      new URL(string);\n      return true;\n    } catch {\n      return false;\n    }\n  };\n\n  const getUrlCount = () => {\n    return urlText\n      .split('\\n')\n      .map(url => url.trim())\n      .filter(url => url.length > 0 && isValidUrl(url)).length;\n  };\n\n  if (!isExpanded) {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8\">\n        <button\n          onClick={() => setIsExpanded(true)}\n          className=\"w-full flex items-center justify-between text-left\"\n        >\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white\">\n              📦 Batch Download\n            </h3>\n            <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n              Download multiple videos or playlists at once\n            </p>\n          </div>\n          <svg className=\"w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n          </svg>\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white\">\n            📦 Batch Download\n          </h3>\n          <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n            Enter multiple URLs (one per line) or playlist URLs\n          </p>\n        </div>\n        <button\n          onClick={() => setIsExpanded(false)}\n          className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-200\"\n        >\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 15l7-7 7 7\" />\n          </svg>\n        </button>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-4\">\n        {/* URL Input */}\n        <div>\n          <label htmlFor=\"batch-urls\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            URLs or Playlist Links\n          </label>\n          <textarea\n            id=\"batch-urls\"\n            value={urlText}\n            onChange={(e) => setUrlText(e.target.value)}\n            placeholder=\"https://www.youtube.com/watch?v=example1&#10;https://www.youtube.com/watch?v=example2&#10;https://www.youtube.com/playlist?list=example\"\n            rows={6}\n            className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-vertical\"\n          />\n          <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n            Valid URLs detected: {getUrlCount()}\n          </p>\n        </div>\n\n        {/* Download Type */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Download Type\n            </label>\n            <div className=\"flex space-x-4\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  name=\"batchDownloadType\"\n                  value=\"video\"\n                  checked={downloadType === 'video'}\n                  onChange={(e) => setDownloadType(e.target.value as 'video' | 'audio')}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm\">🎥 Video</span>\n              </label>\n              <label className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  name=\"batchDownloadType\"\n                  value=\"audio\"\n                  checked={downloadType === 'audio'}\n                  onChange={(e) => setDownloadType(e.target.value as 'video' | 'audio')}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm\">🎵 Audio Only</span>\n              </label>\n            </div>\n          </div>\n\n          {/* Format/Quality Selection */}\n          <div>\n            {downloadType === 'audio' ? (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Audio Format\n                </label>\n                <select\n                  value={audioFormat}\n                  onChange={(e) => setAudioFormat(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white\"\n                >\n                  <option value=\"mp3\">MP3</option>\n                  <option value=\"m4a\">M4A</option>\n                  <option value=\"wav\">WAV</option>\n                  <option value=\"flac\">FLAC</option>\n                  <option value=\"ogg\">OGG</option>\n                </select>\n              </div>\n            ) : (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Video Quality\n                </label>\n                <select\n                  value={quality}\n                  onChange={(e) => setQuality(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white\"\n                >\n                  <option value=\"\">Best Available</option>\n                  <option value=\"720\">720p</option>\n                  <option value=\"480\">480p</option>\n                  <option value=\"360\">360p</option>\n                  <option value=\"240\">240p</option>\n                </select>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Submit Button */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"text-sm text-gray-600 dark:text-gray-300\">\n            {getUrlCount() > 0 && (\n              <span>Ready to download {getUrlCount()} item{getUrlCount() !== 1 ? 's' : ''}</span>\n            )}\n          </div>\n          <button\n            type=\"submit\"\n            disabled={getUrlCount() === 0}\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            Start Batch Download\n          </button>\n        </div>\n      </form>\n\n      {/* Tips */}\n      <div className=\"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg\">\n        <h4 className=\"text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">💡 Tips:</h4>\n        <ul className=\"text-xs text-blue-700 dark:text-blue-300 space-y-1\">\n          <li>• Playlist URLs will automatically download all videos in the playlist</li>\n          <li>• Each URL should be on a separate line</li>\n          <li>• Invalid URLs will be automatically filtered out</li>\n          <li>• Large batches may take significant time to complete</li>\n        </ul>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAee,SAAS,gBAAgB,EAAE,eAAe,EAAsB;IAC7E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,sCAAsC;QACtC,MAAM,OAAO,QACV,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IACnB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,GAAG,KAAK,WAAW;QAE9C,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,MAAM;YACN;QACF;QAEA,MAAM,UAAgC;YACpC,MAAM;YACN,aAAa,iBAAiB,UAAU,cAAc;YACtD,SAAS,iBAAiB,UAAU,UAAU;QAChD;QAEA,gBAAgB,MAAM;QACtB,WAAW,KAAK,uCAAuC;IACzD;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI;YACF,IAAI,IAAI;YACR,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,cAAc;QAClB,OAAO,QACJ,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IACnB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,GAAG,KAAK,WAAW,MAAM,MAAM;IAC5D;IAEA,IAAI,CAAC,YAAY;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBACC,SAAS,IAAM,cAAc;gBAC7B,WAAU;;kCAEV,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,8OAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAI1D,8OAAC;wBAAI,WAAU;wBAAwB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC/E,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;IAK/E;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,8OAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAI1D,8OAAC;wBACC,SAAS,IAAM,cAAc;wBAC7B,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAK3E,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAa,WAAU;0CAAkE;;;;;;0CAGxG,8OAAC;gCACC,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC1C,aAAY;gCACZ,MAAM;gCACN,WAAU;;;;;;0CAEZ,8OAAC;gCAAE,WAAU;;oCAAgD;oCACrC;;;;;;;;;;;;;kCAK1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAM;wDACN,SAAS,iBAAiB;wDAC1B,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDAC/C,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAE5B,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAM;wDACN,SAAS,iBAAiB;wDAC1B,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDAC/C,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAMhC,8OAAC;0CACE,iBAAiB,wBAChB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;;;;;;;;;;;;yDAIxB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC1C,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,mBACf,8OAAC;;wCAAK;wCAAmB;wCAAc;wCAAM,kBAAkB,IAAI,MAAM;;;;;;;;;;;;0CAG7E,8OAAC;gCACC,MAAK;gCACL,UAAU,kBAAkB;gCAC5B,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAC1E,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAKd", "debugId": null}}, {"offset": {"line": 2086, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/SettingsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface Settings {\n  defaultQuality: string;\n  defaultAudioFormat: string;\n  autoSubtitles: boolean;\n  defaultSubtitleLanguages: string;\n  maxConcurrentDownloads: number;\n  downloadPath: string;\n  autoRetry: boolean;\n  retryAttempts: number;\n  bandwidthLimit: number;\n  darkMode: boolean;\n  notifications: boolean;\n}\n\ninterface SettingsPanelProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSettingsChange: (settings: Settings) => void;\n}\n\nconst defaultSettings: Settings = {\n  defaultQuality: 'best',\n  defaultAudioFormat: 'mp3',\n  autoSubtitles: false,\n  defaultSubtitleLanguages: 'en',\n  maxConcurrentDownloads: 3,\n  downloadPath: './downloads',\n  autoRetry: true,\n  retryAttempts: 3,\n  bandwidthLimit: 0,\n  darkMode: false,\n  notifications: true,\n};\n\nexport default function SettingsPanel({ isOpen, onClose, onSettingsChange }: SettingsPanelProps) {\n  const [settings, setSettings] = useState<Settings>(defaultSettings);\n  const [hasChanges, setHasChanges] = useState(false);\n\n  useEffect(() => {\n    // Load settings from localStorage\n    const savedSettings = localStorage.getItem('videoDownloaderSettings');\n    if (savedSettings) {\n      try {\n        const parsed = JSON.parse(savedSettings);\n        setSettings({ ...defaultSettings, ...parsed });\n      } catch (error) {\n        console.error('Failed to parse saved settings:', error);\n      }\n    }\n  }, []);\n\n  const updateSetting = <K extends keyof Settings>(key: K, value: Settings[K]) => {\n    const newSettings = { ...settings, [key]: value };\n    setSettings(newSettings);\n    setHasChanges(true);\n  };\n\n  const saveSettings = () => {\n    localStorage.setItem('videoDownloaderSettings', JSON.stringify(settings));\n    onSettingsChange(settings);\n    setHasChanges(false);\n  };\n\n  const resetSettings = () => {\n    setSettings(defaultSettings);\n    setHasChanges(true);\n  };\n\n  const exportSettings = () => {\n    const dataStr = JSON.stringify(settings, null, 2);\n    const dataBlob = new Blob([dataStr], { type: 'application/json' });\n    const url = URL.createObjectURL(dataBlob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = 'video-downloader-settings.json';\n    link.click();\n    URL.revokeObjectURL(url);\n  };\n\n  const importSettings = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        try {\n          const imported = JSON.parse(e.target?.result as string);\n          setSettings({ ...defaultSettings, ...imported });\n          setHasChanges(true);\n        } catch (error) {\n          alert('Failed to import settings. Please check the file format.');\n        }\n      };\n      reader.readAsText(file);\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden\">\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-600\">\n          <h2 className=\"text-xl font-semibold text-gray-800 dark:text-white\">\n            ⚙️ Settings & Preferences\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-200\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        <div className=\"p-6 overflow-y-auto max-h-[calc(90vh-140px)]\">\n          <div className=\"space-y-6\">\n            {/* Download Defaults */}\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-800 dark:text-white mb-4\">\n                📥 Download Defaults\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Default Video Quality\n                  </label>\n                  <select\n                    value={settings.defaultQuality}\n                    onChange={(e) => updateSetting('defaultQuality', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white\"\n                  >\n                    <option value=\"best\">Best Available</option>\n                    <option value=\"1080\">1080p</option>\n                    <option value=\"720\">720p</option>\n                    <option value=\"480\">480p</option>\n                    <option value=\"360\">360p</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Default Audio Format\n                  </label>\n                  <select\n                    value={settings.defaultAudioFormat}\n                    onChange={(e) => updateSetting('defaultAudioFormat', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white\"\n                  >\n                    <option value=\"mp3\">MP3</option>\n                    <option value=\"m4a\">M4A</option>\n                    <option value=\"wav\">WAV</option>\n                    <option value=\"flac\">FLAC</option>\n                    <option value=\"ogg\">OGG</option>\n                  </select>\n                </div>\n              </div>\n            </div>\n\n            {/* Subtitle Settings */}\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-800 dark:text-white mb-4\">\n                📝 Subtitle Settings\n              </h3>\n              <div className=\"space-y-4\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={settings.autoSubtitles}\n                    onChange={(e) => updateSetting('autoSubtitles', e.target.checked)}\n                    className=\"mr-2\"\n                  />\n                  <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                    Automatically download subtitles\n                  </span>\n                </label>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Default Subtitle Languages\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={settings.defaultSubtitleLanguages}\n                    onChange={(e) => updateSetting('defaultSubtitleLanguages', e.target.value)}\n                    placeholder=\"en,es,fr\"\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white\"\n                  />\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                    Comma-separated language codes\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Performance Settings */}\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-800 dark:text-white mb-4\">\n                🚀 Performance Settings\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Max Concurrent Downloads\n                  </label>\n                  <input\n                    type=\"number\"\n                    min=\"1\"\n                    max=\"10\"\n                    value={settings.maxConcurrentDownloads}\n                    onChange={(e) => updateSetting('maxConcurrentDownloads', parseInt(e.target.value))}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Bandwidth Limit (MB/s, 0 = unlimited)\n                  </label>\n                  <input\n                    type=\"number\"\n                    min=\"0\"\n                    value={settings.bandwidthLimit}\n                    onChange={(e) => updateSetting('bandwidthLimit', parseInt(e.target.value))}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Error Handling */}\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-800 dark:text-white mb-4\">\n                🔄 Error Handling\n              </h3>\n              <div className=\"space-y-4\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={settings.autoRetry}\n                    onChange={(e) => updateSetting('autoRetry', e.target.checked)}\n                    className=\"mr-2\"\n                  />\n                  <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                    Automatically retry failed downloads\n                  </span>\n                </label>\n\n                {settings.autoRetry && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      Retry Attempts\n                    </label>\n                    <input\n                      type=\"number\"\n                      min=\"1\"\n                      max=\"10\"\n                      value={settings.retryAttempts}\n                      onChange={(e) => updateSetting('retryAttempts', parseInt(e.target.value))}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white\"\n                    />\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Interface Settings */}\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-800 dark:text-white mb-4\">\n                🎨 Interface Settings\n              </h3>\n              <div className=\"space-y-4\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={settings.notifications}\n                    onChange={(e) => updateSetting('notifications', e.target.checked)}\n                    className=\"mr-2\"\n                  />\n                  <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                    Enable download notifications\n                  </span>\n                </label>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-600\">\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={exportSettings}\n              className=\"px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n            >\n              Export\n            </button>\n            <label className=\"px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors cursor-pointer\">\n              Import\n              <input\n                type=\"file\"\n                accept=\".json\"\n                onChange={importSettings}\n                className=\"hidden\"\n              />\n            </label>\n            <button\n              onClick={resetSettings}\n              className=\"px-3 py-2 text-sm bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded hover:bg-red-200 dark:hover:bg-red-900/40 transition-colors\"\n            >\n              Reset\n            </button>\n          </div>\n\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={onClose}\n              className=\"px-4 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n            >\n              Cancel\n            </button>\n            <button\n              onClick={() => {\n                saveSettings();\n                onClose();\n              }}\n              disabled={!hasChanges}\n              className=\"px-4 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n            >\n              Save Changes\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAwBA,MAAM,kBAA4B;IAChC,gBAAgB;IAChB,oBAAoB;IACpB,eAAe;IACf,0BAA0B;IAC1B,wBAAwB;IACxB,cAAc;IACd,WAAW;IACX,eAAe;IACf,gBAAgB;IAChB,UAAU;IACV,eAAe;AACjB;AAEe,SAAS,cAAc,EAAE,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAsB;IAC7F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kCAAkC;QAClC,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,IAAI,eAAe;YACjB,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,YAAY;oBAAE,GAAG,eAAe;oBAAE,GAAG,MAAM;gBAAC;YAC9C,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;YACnD;QACF;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAA2B,KAAQ;QACvD,MAAM,cAAc;YAAE,GAAG,QAAQ;YAAE,CAAC,IAAI,EAAE;QAAM;QAChD,YAAY;QACZ,cAAc;IAChB;IAEA,MAAM,eAAe;QACnB,aAAa,OAAO,CAAC,2BAA2B,KAAK,SAAS,CAAC;QAC/D,iBAAiB;QACjB,cAAc;IAChB;IAEA,MAAM,gBAAgB;QACpB,YAAY;QACZ,cAAc;IAChB;IAEA,MAAM,iBAAiB;QACrB,MAAM,UAAU,KAAK,SAAS,CAAC,UAAU,MAAM;QAC/C,MAAM,WAAW,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAmB;QAChE,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG;QAChB,KAAK,KAAK;QACV,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,IAAI;oBACF,MAAM,WAAW,KAAK,KAAK,CAAC,EAAE,MAAM,EAAE;oBACtC,YAAY;wBAAE,GAAG,eAAe;wBAAE,GAAG,QAAQ;oBAAC;oBAC9C,cAAc;gBAChB,EAAE,OAAO,OAAO;oBACd,MAAM;gBACR;YACF;YACA,OAAO,UAAU,CAAC;QACpB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8BAK3E,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyD;;;;;;kDAGvE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,OAAO,SAAS,cAAc;wDAC9B,UAAU,CAAC,IAAM,cAAc,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDAC/D,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAO;;;;;;0EACrB,8OAAC;gEAAO,OAAM;0EAAO;;;;;;0EACrB,8OAAC;gEAAO,OAAM;0EAAM;;;;;;0EACpB,8OAAC;gEAAO,OAAM;0EAAM;;;;;;0EACpB,8OAAC;gEAAO,OAAM;0EAAM;;;;;;;;;;;;;;;;;;0DAIxB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,OAAO,SAAS,kBAAkB;wDAClC,UAAU,CAAC,IAAM,cAAc,sBAAsB,EAAE,MAAM,CAAC,KAAK;wDACnE,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAM;;;;;;0EACpB,8OAAC;gEAAO,OAAM;0EAAM;;;;;;0EACpB,8OAAC;gEAAO,OAAM;0EAAM;;;;;;0EACpB,8OAAC;gEAAO,OAAM;0EAAO;;;;;;0EACrB,8OAAC;gEAAO,OAAM;0EAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO5B,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyD;;;;;;kDAGvE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,SAAS,SAAS,aAAa;wDAC/B,UAAU,CAAC,IAAM,cAAc,iBAAiB,EAAE,MAAM,CAAC,OAAO;wDAChE,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;0DAK7D,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,MAAK;wDACL,OAAO,SAAS,wBAAwB;wDACxC,UAAU,CAAC,IAAM,cAAc,4BAA4B,EAAE,MAAM,CAAC,KAAK;wDACzE,aAAY;wDACZ,WAAU;;;;;;kEAEZ,8OAAC;wDAAE,WAAU;kEAAgD;;;;;;;;;;;;;;;;;;;;;;;;0CAQnE,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyD;;;;;;kDAGvE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,OAAO,SAAS,sBAAsB;wDACtC,UAAU,CAAC,IAAM,cAAc,0BAA0B,SAAS,EAAE,MAAM,CAAC,KAAK;wDAChF,WAAU;;;;;;;;;;;;0DAId,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,OAAO,SAAS,cAAc;wDAC9B,UAAU,CAAC,IAAM,cAAc,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wDACxE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAOlB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyD;;;;;;kDAGvE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,SAAS,SAAS,SAAS;wDAC3B,UAAU,CAAC,IAAM,cAAc,aAAa,EAAE,MAAM,CAAC,OAAO;wDAC5D,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;4CAK5D,SAAS,SAAS,kBACjB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,OAAO,SAAS,aAAa;wDAC7B,UAAU,CAAC,IAAM,cAAc,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK;wDACvE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAQpB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyD;;;;;;kDAGvE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,SAAS,aAAa;oDAC/B,UAAU,CAAC,IAAM,cAAc,iBAAiB,EAAE,MAAM,CAAC,OAAO;oDAChE,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASrE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCAAM,WAAU;;wCAAoK;sDAEnL,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,UAAU;4CACV,WAAU;;;;;;;;;;;;8CAGd,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;sCAKH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;wCACP;wCACA;oCACF;oCACA,UAAU,CAAC;oCACX,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 2803, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/DownloadQueue.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface QueueItem {\n  id: string;\n  url: string;\n  title: string;\n  status: 'queued' | 'downloading' | 'paused' | 'completed' | 'failed';\n  progress: number;\n  priority: 'low' | 'normal' | 'high';\n  addedAt: number;\n  startedAt?: number;\n  completedAt?: number;\n  error?: string;\n  downloadType: 'video' | 'audio';\n  format?: string;\n  quality?: string;\n  audioFormat?: string;\n  estimatedSize?: number;\n  downloadSpeed?: number;\n  eta?: number;\n}\n\ninterface DownloadQueueProps {\n  queue: QueueItem[];\n  onPause: (id: string) => void;\n  onResume: (id: string) => void;\n  onCancel: (id: string) => void;\n  onRetry: (id: string) => void;\n  onPriorityChange: (id: string, priority: 'low' | 'normal' | 'high') => void;\n  onReorder: (fromIndex: number, toIndex: number) => void;\n  maxConcurrent: number;\n}\n\nexport default function DownloadQueue({\n  queue,\n  onPause,\n  onResume,\n  onCancel,\n  onRetry,\n  onPriorityChange,\n  onReorder,\n  maxConcurrent\n}: DownloadQueueProps) {\n  const [isExpanded, setIsExpanded] = useState(true);\n  const [filter, setFilter] = useState<'all' | 'active' | 'completed' | 'failed'>('all');\n\n  const formatSpeed = (bytesPerSecond: number) => {\n    if (!bytesPerSecond) return 'Unknown';\n    const units = ['B/s', 'KB/s', 'MB/s', 'GB/s'];\n    let size = bytesPerSecond;\n    let unitIndex = 0;\n    \n    while (size >= 1024 && unitIndex < units.length - 1) {\n      size /= 1024;\n      unitIndex++;\n    }\n    \n    return `${size.toFixed(1)} ${units[unitIndex]}`;\n  };\n\n  const formatETA = (seconds: number) => {\n    if (!seconds || seconds === Infinity) return 'Unknown';\n    \n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = Math.floor(seconds % 60);\n    \n    if (hours > 0) {\n      return `${hours}h ${minutes}m`;\n    } else if (minutes > 0) {\n      return `${minutes}m ${secs}s`;\n    } else {\n      return `${secs}s`;\n    }\n  };\n\n  const formatFileSize = (bytes: number) => {\n    if (!bytes) return 'Unknown';\n    const units = ['B', 'KB', 'MB', 'GB'];\n    let size = bytes;\n    let unitIndex = 0;\n    \n    while (size >= 1024 && unitIndex < units.length - 1) {\n      size /= 1024;\n      unitIndex++;\n    }\n    \n    return `${size.toFixed(1)} ${units[unitIndex]}`;\n  };\n\n  const getStatusIcon = (status: QueueItem['status']) => {\n    switch (status) {\n      case 'queued': return '⏳';\n      case 'downloading': return '⬇️';\n      case 'paused': return '⏸️';\n      case 'completed': return '✅';\n      case 'failed': return '❌';\n      default: return '❓';\n    }\n  };\n\n  const getPriorityColor = (priority: QueueItem['priority']) => {\n    switch (priority) {\n      case 'high': return 'text-red-600 dark:text-red-400';\n      case 'normal': return 'text-blue-600 dark:text-blue-400';\n      case 'low': return 'text-gray-600 dark:text-gray-400';\n    }\n  };\n\n  const filteredQueue = queue.filter(item => {\n    switch (filter) {\n      case 'active': return ['queued', 'downloading', 'paused'].includes(item.status);\n      case 'completed': return item.status === 'completed';\n      case 'failed': return item.status === 'failed';\n      default: return true;\n    }\n  });\n\n  const activeDownloads = queue.filter(item => item.status === 'downloading').length;\n  const queuedItems = queue.filter(item => item.status === 'queued').length;\n  const completedItems = queue.filter(item => item.status === 'completed').length;\n  const failedItems = queue.filter(item => item.status === 'failed').length;\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg\">\n      <div \n        className=\"flex items-center justify-between p-4 cursor-pointer\"\n        onClick={() => setIsExpanded(!isExpanded)}\n      >\n        <div className=\"flex items-center space-x-3\">\n          <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white\">\n            📋 Download Queue\n          </h3>\n          <div className=\"flex space-x-2 text-sm\">\n            <span className=\"px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded\">\n              {activeDownloads}/{maxConcurrent} Active\n            </span>\n            <span className=\"px-2 py-1 bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200 rounded\">\n              {queuedItems} Queued\n            </span>\n            <span className=\"px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 rounded\">\n              {completedItems} Done\n            </span>\n            {failedItems > 0 && (\n              <span className=\"px-2 py-1 bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200 rounded\">\n                {failedItems} Failed\n              </span>\n            )}\n          </div>\n        </div>\n        <svg \n          className={`w-5 h-5 text-gray-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`} \n          fill=\"none\" \n          stroke=\"currentColor\" \n          viewBox=\"0 0 24 24\"\n        >\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n        </svg>\n      </div>\n\n      {isExpanded && (\n        <div className=\"border-t border-gray-200 dark:border-gray-600\">\n          {/* Filter Controls */}\n          <div className=\"p-4 border-b border-gray-200 dark:border-gray-600\">\n            <div className=\"flex space-x-2\">\n              {(['all', 'active', 'completed', 'failed'] as const).map((filterType) => (\n                <button\n                  key={filterType}\n                  onClick={() => setFilter(filterType)}\n                  className={`px-3 py-1 text-sm rounded transition-colors ${\n                    filter === filterType\n                      ? 'bg-blue-600 text-white'\n                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'\n                  }`}\n                >\n                  {filterType.charAt(0).toUpperCase() + filterType.slice(1)}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Queue Items */}\n          <div className=\"max-h-96 overflow-y-auto\">\n            {filteredQueue.length === 0 ? (\n              <div className=\"p-8 text-center text-gray-500 dark:text-gray-400\">\n                <p>No items in queue</p>\n                <p className=\"text-sm mt-1\">Downloads will appear here when you start them</p>\n              </div>\n            ) : (\n              <div className=\"space-y-2 p-4\">\n                {filteredQueue.map((item, index) => (\n                  <div\n                    key={item.id}\n                    className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n                  >\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <div className=\"flex items-center space-x-3 flex-1 min-w-0\">\n                        <span className=\"text-lg\">{getStatusIcon(item.status)}</span>\n                        <div className=\"flex-1 min-w-0\">\n                          <p className=\"text-sm font-medium text-gray-800 dark:text-white truncate\">\n                            {item.title || 'Unknown Title'}\n                          </p>\n                          <div className=\"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400\">\n                            <span className={getPriorityColor(item.priority)}>\n                              {item.priority.toUpperCase()}\n                            </span>\n                            <span>•</span>\n                            <span>{item.downloadType === 'audio' ? '🎵' : '🎥'}</span>\n                            <span>•</span>\n                            <span>{formatFileSize(item.estimatedSize || 0)}</span>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Action Buttons */}\n                      <div className=\"flex items-center space-x-1\">\n                        {item.status === 'downloading' && (\n                          <button\n                            onClick={() => onPause(item.id)}\n                            className=\"p-1 text-yellow-600 hover:text-yellow-700 dark:text-yellow-400\"\n                            title=\"Pause\"\n                          >\n                            ⏸️\n                          </button>\n                        )}\n                        \n                        {item.status === 'paused' && (\n                          <button\n                            onClick={() => onResume(item.id)}\n                            className=\"p-1 text-green-600 hover:text-green-700 dark:text-green-400\"\n                            title=\"Resume\"\n                          >\n                            ▶️\n                          </button>\n                        )}\n                        \n                        {item.status === 'failed' && (\n                          <button\n                            onClick={() => onRetry(item.id)}\n                            className=\"p-1 text-blue-600 hover:text-blue-700 dark:text-blue-400\"\n                            title=\"Retry\"\n                          >\n                            🔄\n                          </button>\n                        )}\n                        \n                        {['queued', 'downloading', 'paused'].includes(item.status) && (\n                          <button\n                            onClick={() => onCancel(item.id)}\n                            className=\"p-1 text-red-600 hover:text-red-700 dark:text-red-400\"\n                            title=\"Cancel\"\n                          >\n                            ❌\n                          </button>\n                        )}\n\n                        {/* Priority Selector */}\n                        {item.status === 'queued' && (\n                          <select\n                            value={item.priority}\n                            onChange={(e) => onPriorityChange(item.id, e.target.value as any)}\n                            className=\"text-xs border border-gray-300 dark:border-gray-600 rounded px-1 py-0.5 dark:bg-gray-700 dark:text-white\"\n                          >\n                            <option value=\"low\">Low</option>\n                            <option value=\"normal\">Normal</option>\n                            <option value=\"high\">High</option>\n                          </select>\n                        )}\n                      </div>\n                    </div>\n\n                    {/* Progress Bar */}\n                    {['downloading', 'paused'].includes(item.status) && (\n                      <div className=\"mb-2\">\n                        <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                          <div\n                            className={`h-2 rounded-full transition-all duration-300 ${\n                              item.status === 'downloading' ? 'bg-blue-600' : 'bg-yellow-600'\n                            }`}\n                            style={{ width: `${Math.max(0, Math.min(100, item.progress))}%` }}\n                          ></div>\n                        </div>\n                        <div className=\"flex justify-between items-center mt-1 text-xs text-gray-500 dark:text-gray-400\">\n                          <span>{item.progress.toFixed(1)}%</span>\n                          {item.downloadSpeed && (\n                            <span>{formatSpeed(item.downloadSpeed)}</span>\n                          )}\n                          {item.eta && (\n                            <span>ETA: {formatETA(item.eta)}</span>\n                          )}\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Error Message */}\n                    {item.status === 'failed' && item.error && (\n                      <div className=\"mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded\">\n                        <p className=\"text-xs text-red-800 dark:text-red-200\">\n                          Error: {item.error}\n                        </p>\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAmCe,SAAS,cAAc,EACpC,KAAK,EACL,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,gBAAgB,EAChB,SAAS,EACT,aAAa,EACM;IACnB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6C;IAEhF,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,gBAAgB,OAAO;QAC5B,MAAM,QAAQ;YAAC;YAAO;YAAQ;YAAQ;SAAO;QAC7C,IAAI,OAAO;QACX,IAAI,YAAY;QAEhB,MAAO,QAAQ,QAAQ,YAAY,MAAM,MAAM,GAAG,EAAG;YACnD,QAAQ;YACR;QACF;QAEA,OAAO,GAAG,KAAK,OAAO,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,UAAU,EAAE;IACjD;IAEA,MAAM,YAAY,CAAC;QACjB,IAAI,CAAC,WAAW,YAAY,UAAU,OAAO;QAE7C,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;QAC9C,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAElC,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;QAChC,OAAO,IAAI,UAAU,GAAG;YACtB,OAAO,GAAG,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;QAC/B,OAAO;YACL,OAAO,GAAG,KAAK,CAAC,CAAC;QACnB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,OAAO,OAAO;QACnB,MAAM,QAAQ;YAAC;YAAK;YAAM;YAAM;SAAK;QACrC,IAAI,OAAO;QACX,IAAI,YAAY;QAEhB,MAAO,QAAQ,QAAQ,YAAY,MAAM,MAAM,GAAG,EAAG;YACnD,QAAQ;YACR;QACF;QAEA,OAAO,GAAG,KAAK,OAAO,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,UAAU,EAAE;IACjD;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;QACrB;IACF;IAEA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,OAAQ;YACN,KAAK;gBAAU,OAAO;oBAAC;oBAAU;oBAAe;iBAAS,CAAC,QAAQ,CAAC,KAAK,MAAM;YAC9E,KAAK;gBAAa,OAAO,KAAK,MAAM,KAAK;YACzC,KAAK;gBAAU,OAAO,KAAK,MAAM,KAAK;YACtC;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,eAAe,MAAM;IAClF,MAAM,cAAc,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,UAAU,MAAM;IACzE,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,aAAa,MAAM;IAC/E,MAAM,cAAc,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,UAAU,MAAM;IAEzE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,cAAc,CAAC;;kCAE9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CACb;4CAAgB;4CAAE;4CAAc;;;;;;;kDAEnC,8OAAC;wCAAK,WAAU;;4CACb;4CAAY;;;;;;;kDAEf,8OAAC;wCAAK,WAAU;;4CACb;4CAAe;;;;;;;oCAEjB,cAAc,mBACb,8OAAC;wCAAK,WAAU;;4CACb;4CAAY;;;;;;;;;;;;;;;;;;;kCAKrB,8OAAC;wBACC,WAAW,CAAC,2CAA2C,EAAE,aAAa,eAAe,IAAI;wBACzF,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;YAIxE,4BACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,AAAC;gCAAC;gCAAO;gCAAU;gCAAa;6BAAS,CAAW,GAAG,CAAC,CAAC,2BACxD,8OAAC;oCAEC,SAAS,IAAM,UAAU;oCACzB,WAAW,CAAC,4CAA4C,EACtD,WAAW,aACP,2BACA,0GACJ;8CAED,WAAW,MAAM,CAAC,GAAG,WAAW,KAAK,WAAW,KAAK,CAAC;mCARlD;;;;;;;;;;;;;;;kCAeb,8OAAC;wBAAI,WAAU;kCACZ,cAAc,MAAM,KAAK,kBACxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAE,WAAU;8CAAe;;;;;;;;;;;iDAG9B,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,8OAAC;oCAEC,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAW,cAAc,KAAK,MAAM;;;;;;sEACpD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EACV,KAAK,KAAK,IAAI;;;;;;8EAEjB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAW,iBAAiB,KAAK,QAAQ;sFAC5C,KAAK,QAAQ,CAAC,WAAW;;;;;;sFAE5B,8OAAC;sFAAK;;;;;;sFACN,8OAAC;sFAAM,KAAK,YAAY,KAAK,UAAU,OAAO;;;;;;sFAC9C,8OAAC;sFAAK;;;;;;sFACN,8OAAC;sFAAM,eAAe,KAAK,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;;;;;8DAMlD,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,MAAM,KAAK,+BACf,8OAAC;4DACC,SAAS,IAAM,QAAQ,KAAK,EAAE;4DAC9B,WAAU;4DACV,OAAM;sEACP;;;;;;wDAKF,KAAK,MAAM,KAAK,0BACf,8OAAC;4DACC,SAAS,IAAM,SAAS,KAAK,EAAE;4DAC/B,WAAU;4DACV,OAAM;sEACP;;;;;;wDAKF,KAAK,MAAM,KAAK,0BACf,8OAAC;4DACC,SAAS,IAAM,QAAQ,KAAK,EAAE;4DAC9B,WAAU;4DACV,OAAM;sEACP;;;;;;wDAKF;4DAAC;4DAAU;4DAAe;yDAAS,CAAC,QAAQ,CAAC,KAAK,MAAM,mBACvD,8OAAC;4DACC,SAAS,IAAM,SAAS,KAAK,EAAE;4DAC/B,WAAU;4DACV,OAAM;sEACP;;;;;;wDAMF,KAAK,MAAM,KAAK,0BACf,8OAAC;4DACC,OAAO,KAAK,QAAQ;4DACpB,UAAU,CAAC,IAAM,iBAAiB,KAAK,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;4DACzD,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAM;;;;;;8EACpB,8OAAC;oEAAO,OAAM;8EAAS;;;;;;8EACvB,8OAAC;oEAAO,OAAM;8EAAO;;;;;;;;;;;;;;;;;;;;;;;;wCAO5B;4CAAC;4CAAe;yCAAS,CAAC,QAAQ,CAAC,KAAK,MAAM,mBAC7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,WAAW,CAAC,6CAA6C,EACvD,KAAK,MAAM,KAAK,gBAAgB,gBAAgB,iBAChD;wDACF,OAAO;4DAAE,OAAO,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,QAAQ,GAAG,CAAC,CAAC;wDAAC;;;;;;;;;;;8DAGpE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;gEAAM,KAAK,QAAQ,CAAC,OAAO,CAAC;gEAAG;;;;;;;wDAC/B,KAAK,aAAa,kBACjB,8OAAC;sEAAM,YAAY,KAAK,aAAa;;;;;;wDAEtC,KAAK,GAAG,kBACP,8OAAC;;gEAAK;gEAAM,UAAU,KAAK,GAAG;;;;;;;;;;;;;;;;;;;wCAOrC,KAAK,MAAM,KAAK,YAAY,KAAK,KAAK,kBACrC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;;oDAAyC;oDAC5C,KAAK,KAAK;;;;;;;;;;;;;mCA1GnB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuHhC", "debugId": null}}, {"offset": {"line": 3365, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/data/supportedSites.ts"], "sourcesContent": ["export interface SiteInfo {\n  name: string;\n  domain: string;\n  icon: string;\n  category: string;\n  features: string[];\n  examples: string[];\n  description: string;\n  supportsPlaylists: boolean;\n  supportsLive: boolean;\n  supportsSubtitles: boolean;\n  maxQuality: string;\n  audioFormats: string[];\n  notes?: string;\n}\n\nexport const supportedSites: SiteInfo[] = [\n  // Video Platforms\n  {\n    name: \"YouTube\",\n    domain: \"youtube.com\",\n    icon: \"🎥\",\n    category: \"Video Platforms\",\n    features: [\"4K/8K Video\", \"Live Streams\", \"Playlists\", \"Subtitles\", \"Audio Extraction\"],\n    examples: [\"https://youtube.com/watch?v=...\", \"https://youtube.com/playlist?list=...\"],\n    description: \"World's largest video platform with comprehensive format support\",\n    supportsPlaylists: true,\n    supportsLive: true,\n    supportsSubtitles: true,\n    maxQuality: \"8K\",\n    audioFormats: [\"mp3\", \"m4a\", \"opus\", \"wav\"]\n  },\n  {\n    name: \"Vimeo\",\n    domain: \"vimeo.com\",\n    icon: \"🎬\",\n    category: \"Video Platforms\",\n    features: [\"HD Video\", \"Private Videos\", \"Password Protected\", \"Subtitles\"],\n    examples: [\"https://vimeo.com/123456789\"],\n    description: \"High-quality video platform for creators and professionals\",\n    supportsPlaylists: true,\n    supportsLive: false,\n    supportsSubtitles: true,\n    maxQuality: \"4K\",\n    audioFormats: [\"mp3\", \"m4a\"]\n  },\n  {\n    name: \"Dailymotion\",\n    domain: \"dailymotion.com\",\n    icon: \"📺\",\n    category: \"Video Platforms\",\n    features: [\"HD Video\", \"Playlists\", \"Live Streams\"],\n    examples: [\"https://dailymotion.com/video/...\"],\n    description: \"European video platform with diverse content\",\n    supportsPlaylists: true,\n    supportsLive: true,\n    supportsSubtitles: false,\n    maxQuality: \"1080p\",\n    audioFormats: [\"mp3\", \"m4a\"]\n  },\n\n  // Social Media\n  {\n    name: \"TikTok\",\n    domain: \"tiktok.com\",\n    icon: \"🎵\",\n    category: \"Social Media\",\n    features: [\"Short Videos\", \"Audio Extraction\", \"User Profiles\"],\n    examples: [\"https://tiktok.com/@user/video/...\"],\n    description: \"Short-form video platform with viral content\",\n    supportsPlaylists: false,\n    supportsLive: false,\n    supportsSubtitles: false,\n    maxQuality: \"1080p\",\n    audioFormats: [\"mp3\", \"m4a\"]\n  },\n  {\n    name: \"Instagram\",\n    domain: \"instagram.com\",\n    icon: \"📸\",\n    category: \"Social Media\",\n    features: [\"Posts\", \"Stories\", \"Reels\", \"IGTV\"],\n    examples: [\"https://instagram.com/p/...\", \"https://instagram.com/stories/...\"],\n    description: \"Photo and video sharing with stories and reels\",\n    supportsPlaylists: false,\n    supportsLive: true,\n    supportsSubtitles: false,\n    maxQuality: \"1080p\",\n    audioFormats: [\"mp3\", \"m4a\"]\n  },\n  {\n    name: \"Twitter/X\",\n    domain: \"twitter.com\",\n    icon: \"🐦\",\n    category: \"Social Media\",\n    features: [\"Video Tweets\", \"Spaces Audio\", \"Live Streams\"],\n    examples: [\"https://twitter.com/user/status/...\"],\n    description: \"Social media platform with video and audio content\",\n    supportsPlaylists: false,\n    supportsLive: true,\n    supportsSubtitles: false,\n    maxQuality: \"1080p\",\n    audioFormats: [\"mp3\", \"m4a\"]\n  },\n  {\n    name: \"Facebook\",\n    domain: \"facebook.com\",\n    icon: \"👥\",\n    category: \"Social Media\",\n    features: [\"Video Posts\", \"Live Streams\", \"Stories\"],\n    examples: [\"https://facebook.com/watch/...\"],\n    description: \"Social network with extensive video content\",\n    supportsPlaylists: false,\n    supportsLive: true,\n    supportsSubtitles: false,\n    maxQuality: \"1080p\",\n    audioFormats: [\"mp3\", \"m4a\"]\n  },\n\n  // Streaming Platforms\n  {\n    name: \"Twitch\",\n    domain: \"twitch.tv\",\n    icon: \"🎮\",\n    category: \"Streaming\",\n    features: [\"Live Streams\", \"VODs\", \"Clips\", \"Chat Replay\"],\n    examples: [\"https://twitch.tv/videos/...\", \"https://clips.twitch.tv/...\"],\n    description: \"Gaming and live streaming platform\",\n    supportsPlaylists: false,\n    supportsLive: true,\n    supportsSubtitles: false,\n    maxQuality: \"1080p\",\n    audioFormats: [\"mp3\", \"m4a\"]\n  },\n  {\n    name: \"Kick\",\n    domain: \"kick.com\",\n    icon: \"⚡\",\n    category: \"Streaming\",\n    features: [\"Live Streams\", \"VODs\", \"Clips\"],\n    examples: [\"https://kick.com/...\"],\n    description: \"Alternative streaming platform\",\n    supportsPlaylists: false,\n    supportsLive: true,\n    supportsSubtitles: false,\n    maxQuality: \"1080p\",\n    audioFormats: [\"mp3\", \"m4a\"]\n  },\n\n  // News & Media\n  {\n    name: \"BBC iPlayer\",\n    domain: \"bbc.co.uk\",\n    icon: \"📻\",\n    category: \"News & Media\",\n    features: [\"TV Shows\", \"News\", \"Documentaries\", \"Subtitles\"],\n    examples: [\"https://bbc.co.uk/iplayer/episode/...\"],\n    description: \"BBC's streaming service with UK content\",\n    supportsPlaylists: true,\n    supportsLive: true,\n    supportsSubtitles: true,\n    maxQuality: \"1080p\",\n    audioFormats: [\"mp3\", \"m4a\"],\n    notes: \"May require UK IP address\"\n  },\n  {\n    name: \"CNN\",\n    domain: \"cnn.com\",\n    icon: \"📰\",\n    category: \"News & Media\",\n    features: [\"News Videos\", \"Live Streams\", \"Documentaries\"],\n    examples: [\"https://cnn.com/videos/...\"],\n    description: \"News network with video content\",\n    supportsPlaylists: false,\n    supportsLive: true,\n    supportsSubtitles: true,\n    maxQuality: \"1080p\",\n    audioFormats: [\"mp3\", \"m4a\"]\n  },\n\n  // Educational\n  {\n    name: \"Khan Academy\",\n    domain: \"khanacademy.org\",\n    icon: \"🎓\",\n    category: \"Educational\",\n    features: [\"Educational Videos\", \"Courses\", \"Subtitles\"],\n    examples: [\"https://khanacademy.org/...\"],\n    description: \"Free educational content and courses\",\n    supportsPlaylists: true,\n    supportsLive: false,\n    supportsSubtitles: true,\n    maxQuality: \"1080p\",\n    audioFormats: [\"mp3\", \"m4a\"]\n  },\n  {\n    name: \"Coursera\",\n    domain: \"coursera.org\",\n    icon: \"📚\",\n    category: \"Educational\",\n    features: [\"Course Videos\", \"Lectures\", \"Subtitles\"],\n    examples: [\"https://coursera.org/learn/...\"],\n    description: \"Online learning platform with university courses\",\n    supportsPlaylists: true,\n    supportsLive: false,\n    supportsSubtitles: true,\n    maxQuality: \"1080p\",\n    audioFormats: [\"mp3\", \"m4a\"],\n    notes: \"May require enrollment\"\n  },\n\n  // Music & Audio\n  {\n    name: \"SoundCloud\",\n    domain: \"soundcloud.com\",\n    icon: \"🎧\",\n    category: \"Music & Audio\",\n    features: [\"Audio Tracks\", \"Playlists\", \"Podcasts\"],\n    examples: [\"https://soundcloud.com/user/track\"],\n    description: \"Audio platform for music and podcasts\",\n    supportsPlaylists: true,\n    supportsLive: false,\n    supportsSubtitles: false,\n    maxQuality: \"Audio Only\",\n    audioFormats: [\"mp3\", \"m4a\", \"opus\"]\n  },\n  {\n    name: \"Bandcamp\",\n    domain: \"bandcamp.com\",\n    icon: \"🎼\",\n    category: \"Music & Audio\",\n    features: [\"Music Albums\", \"High Quality Audio\", \"Artist Pages\"],\n    examples: [\"https://artist.bandcamp.com/album/...\"],\n    description: \"Independent music platform with high-quality audio\",\n    supportsPlaylists: true,\n    supportsLive: false,\n    supportsSubtitles: false,\n    maxQuality: \"Audio Only\",\n    audioFormats: [\"mp3\", \"flac\", \"wav\"]\n  },\n\n  // International Platforms\n  {\n    name: \"Bilibili\",\n    domain: \"bilibili.com\",\n    icon: \"📱\",\n    category: \"International\",\n    features: [\"Anime\", \"Gaming\", \"Live Streams\", \"Subtitles\"],\n    examples: [\"https://bilibili.com/video/...\"],\n    description: \"Chinese video platform popular for anime and gaming\",\n    supportsPlaylists: true,\n    supportsLive: true,\n    supportsSubtitles: true,\n    maxQuality: \"4K\",\n    audioFormats: [\"mp3\", \"m4a\"]\n  },\n  {\n    name: \"Niconico\",\n    domain: \"nicovideo.jp\",\n    icon: \"🇯🇵\",\n    category: \"International\",\n    features: [\"Japanese Content\", \"Live Streams\", \"Comments\"],\n    examples: [\"https://nicovideo.jp/watch/...\"],\n    description: \"Japanese video platform with unique comment system\",\n    supportsPlaylists: true,\n    supportsLive: true,\n    supportsSubtitles: false,\n    maxQuality: \"1080p\",\n    audioFormats: [\"mp3\", \"m4a\"]\n  },\n\n  // Adult Content (18+)\n  {\n    name: \"Adult Platforms\",\n    domain: \"various\",\n    icon: \"🔞\",\n    category: \"Adult Content\",\n    features: [\"Various adult video platforms\"],\n    examples: [\"Multiple adult video sites supported\"],\n    description: \"Various adult content platforms (18+ only)\",\n    supportsPlaylists: true,\n    supportsLive: true,\n    supportsSubtitles: false,\n    maxQuality: \"4K\",\n    audioFormats: [\"mp3\", \"m4a\"],\n    notes: \"Age verification required\"\n  }\n];\n\nexport const siteCategories = [\n  \"Video Platforms\",\n  \"Social Media\", \n  \"Streaming\",\n  \"News & Media\",\n  \"Educational\",\n  \"Music & Audio\",\n  \"International\",\n  \"Adult Content\"\n];\n\nexport const getTotalSitesCount = () => {\n  // yt-dlp supports 1000+ sites, but we're showcasing the most popular ones\n  return \"1000+\";\n};\n\nexport const findSiteByDomain = (url: string): SiteInfo | null => {\n  try {\n    const domain = new URL(url).hostname.replace('www.', '');\n    return supportedSites.find(site => \n      domain.includes(site.domain.replace('www.', '')) ||\n      site.domain.replace('www.', '').includes(domain)\n    ) || null;\n  } catch {\n    return null;\n  }\n};\n"], "names": [], "mappings": ";;;;;;AAgBO,MAAM,iBAA6B;IACxC,kBAAkB;IAClB;QACE,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAe;YAAgB;YAAa;YAAa;SAAmB;QACvF,UAAU;YAAC;YAAmC;SAAwC;QACtF,aAAa;QACb,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,cAAc;YAAC;YAAO;YAAO;YAAQ;SAAM;IAC7C;IACA;QACE,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAY;YAAkB;YAAsB;SAAY;QAC3E,UAAU;YAAC;SAA8B;QACzC,aAAa;QACb,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,cAAc;YAAC;YAAO;SAAM;IAC9B;IACA;QACE,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAY;YAAa;SAAe;QACnD,UAAU;YAAC;SAAoC;QAC/C,aAAa;QACb,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,cAAc;YAAC;YAAO;SAAM;IAC9B;IAEA,eAAe;IACf;QACE,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAgB;YAAoB;SAAgB;QAC/D,UAAU;YAAC;SAAqC;QAChD,aAAa;QACb,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,cAAc;YAAC;YAAO;SAAM;IAC9B;IACA;QACE,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAS;YAAW;YAAS;SAAO;QAC/C,UAAU;YAAC;YAA+B;SAAoC;QAC9E,aAAa;QACb,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,cAAc;YAAC;YAAO;SAAM;IAC9B;IACA;QACE,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAgB;YAAgB;SAAe;QAC1D,UAAU;YAAC;SAAsC;QACjD,aAAa;QACb,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,cAAc;YAAC;YAAO;SAAM;IAC9B;IACA;QACE,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAe;YAAgB;SAAU;QACpD,UAAU;YAAC;SAAiC;QAC5C,aAAa;QACb,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,cAAc;YAAC;YAAO;SAAM;IAC9B;IAEA,sBAAsB;IACtB;QACE,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAgB;YAAQ;YAAS;SAAc;QAC1D,UAAU;YAAC;YAAgC;SAA8B;QACzE,aAAa;QACb,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,cAAc;YAAC;YAAO;SAAM;IAC9B;IACA;QACE,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAgB;YAAQ;SAAQ;QAC3C,UAAU;YAAC;SAAuB;QAClC,aAAa;QACb,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,cAAc;YAAC;YAAO;SAAM;IAC9B;IAEA,eAAe;IACf;QACE,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAY;YAAQ;YAAiB;SAAY;QAC5D,UAAU;YAAC;SAAwC;QACnD,aAAa;QACb,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,cAAc;YAAC;YAAO;SAAM;QAC5B,OAAO;IACT;IACA;QACE,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAe;YAAgB;SAAgB;QAC1D,UAAU;YAAC;SAA6B;QACxC,aAAa;QACb,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,cAAc;YAAC;YAAO;SAAM;IAC9B;IAEA,cAAc;IACd;QACE,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAsB;YAAW;SAAY;QACxD,UAAU;YAAC;SAA8B;QACzC,aAAa;QACb,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,cAAc;YAAC;YAAO;SAAM;IAC9B;IACA;QACE,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAiB;YAAY;SAAY;QACpD,UAAU;YAAC;SAAiC;QAC5C,aAAa;QACb,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,cAAc;YAAC;YAAO;SAAM;QAC5B,OAAO;IACT;IAEA,gBAAgB;IAChB;QACE,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAgB;YAAa;SAAW;QACnD,UAAU;YAAC;SAAoC;QAC/C,aAAa;QACb,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,cAAc;YAAC;YAAO;YAAO;SAAO;IACtC;IACA;QACE,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAgB;YAAsB;SAAe;QAChE,UAAU;YAAC;SAAwC;QACnD,aAAa;QACb,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,cAAc;YAAC;YAAO;YAAQ;SAAM;IACtC;IAEA,0BAA0B;IAC1B;QACE,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAS;YAAU;YAAgB;SAAY;QAC1D,UAAU;YAAC;SAAiC;QAC5C,aAAa;QACb,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,cAAc;YAAC;YAAO;SAAM;IAC9B;IACA;QACE,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAoB;YAAgB;SAAW;QAC1D,UAAU;YAAC;SAAiC;QAC5C,aAAa;QACb,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,cAAc;YAAC;YAAO;SAAM;IAC9B;IAEA,sBAAsB;IACtB;QACE,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;YAAC;SAAgC;QAC3C,UAAU;YAAC;SAAuC;QAClD,aAAa;QACb,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,cAAc;YAAC;YAAO;SAAM;QAC5B,OAAO;IACT;CACD;AAEM,MAAM,iBAAiB;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,qBAAqB;IAChC,0EAA0E;IAC1E,OAAO;AACT;AAEO,MAAM,mBAAmB,CAAC;IAC/B,IAAI;QACF,MAAM,SAAS,IAAI,IAAI,KAAK,QAAQ,CAAC,OAAO,CAAC,QAAQ;QACrD,OAAO,eAAe,IAAI,CAAC,CAAA,OACzB,OAAO,QAAQ,CAAC,KAAK,MAAM,CAAC,OAAO,CAAC,QAAQ,QAC5C,KAAK,MAAM,CAAC,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC,YACtC;IACP,EAAE,OAAM;QACN,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 3838, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/SiteDirectory.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { supportedSites, siteCategories, getTotalSitesCount, SiteInfo } from '@/data/supportedSites';\n\ninterface SiteDirectoryProps {\n  onSiteSelect?: (site: SiteInfo) => void;\n  onExampleSelect?: (url: string) => void;\n}\n\nexport default function SiteDirectory({ onSiteSelect, onExampleSelect }: SiteDirectoryProps) {\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [selectedCategory, setSelectedCategory] = useState<string>('all');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  const filteredSites = supportedSites.filter(site => {\n    const matchesCategory = selectedCategory === 'all' || site.category === selectedCategory;\n    const matchesSearch = searchTerm === '' || \n      site.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      site.domain.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      site.description.toLowerCase().includes(searchTerm.toLowerCase());\n    \n    return matchesCategory && matchesSearch;\n  });\n\n  const handleExampleClick = (url: string, e: React.MouseEvent) => {\n    e.stopPropagation();\n    if (onExampleSelect) {\n      onExampleSelect(url);\n    }\n  };\n\n  if (!isExpanded) {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8\">\n        <button\n          onClick={() => setIsExpanded(true)}\n          className=\"w-full flex items-center justify-between text-left\"\n        >\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white\">\n              🌐 Supported Sites Directory\n            </h3>\n            <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n              Browse {getTotalSitesCount()} supported websites and platforms\n            </p>\n          </div>\n          <svg className=\"w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n          </svg>\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white\">\n            🌐 Supported Sites Directory\n          </h3>\n          <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n            {getTotalSitesCount()} supported websites across {siteCategories.length} categories\n          </p>\n        </div>\n        <button\n          onClick={() => setIsExpanded(false)}\n          className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-200\"\n        >\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 15l7-7 7 7\" />\n          </svg>\n        </button>\n      </div>\n\n      {/* Search and Filter Controls */}\n      <div className=\"flex flex-col md:flex-row gap-4 mb-6\">\n        <div className=\"flex-1\">\n          <input\n            type=\"text\"\n            placeholder=\"Search sites by name, domain, or description...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n          />\n        </div>\n        <div>\n          <select\n            value={selectedCategory}\n            onChange={(e) => setSelectedCategory(e.target.value)}\n            className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white\"\n          >\n            <option value=\"all\">All Categories</option>\n            {siteCategories.map(category => (\n              <option key={category} value={category}>{category}</option>\n            ))}\n          </select>\n        </div>\n      </div>\n\n      {/* Sites Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto\">\n        {filteredSites.map((site) => (\n          <div\n            key={site.domain}\n            className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer\"\n            onClick={() => onSiteSelect?.(site)}\n          >\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <span className=\"text-2xl\">{site.icon}</span>\n              <div className=\"flex-1 min-w-0\">\n                <h4 className=\"text-sm font-medium text-gray-800 dark:text-white truncate\">\n                  {site.name}\n                </h4>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\n                  {site.domain}\n                </p>\n              </div>\n              <span className=\"px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded\">\n                {site.category}\n              </span>\n            </div>\n\n            <p className=\"text-xs text-gray-600 dark:text-gray-300 mb-3 line-clamp-2\">\n              {site.description}\n            </p>\n\n            {/* Features */}\n            <div className=\"mb-3\">\n              <div className=\"flex flex-wrap gap-1\">\n                {site.features.slice(0, 3).map((feature) => (\n                  <span\n                    key={feature}\n                    className=\"px-2 py-0.5 text-xs bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-300 rounded\"\n                  >\n                    {feature}\n                  </span>\n                ))}\n                {site.features.length > 3 && (\n                  <span className=\"px-2 py-0.5 text-xs bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-300 rounded\">\n                    +{site.features.length - 3} more\n                  </span>\n                )}\n              </div>\n            </div>\n\n            {/* Capabilities */}\n            <div className=\"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-3\">\n              <div className=\"flex space-x-2\">\n                {site.supportsPlaylists && <span title=\"Supports Playlists\">📋</span>}\n                {site.supportsLive && <span title=\"Supports Live Streams\">🔴</span>}\n                {site.supportsSubtitles && <span title=\"Supports Subtitles\">📝</span>}\n              </div>\n              <span className=\"font-medium\">{site.maxQuality}</span>\n            </div>\n\n            {/* Example URLs */}\n            {site.examples.length > 0 && (\n              <div>\n                <p className=\"text-xs font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Example URLs:\n                </p>\n                <div className=\"space-y-1\">\n                  {site.examples.slice(0, 2).map((example, index) => (\n                    <button\n                      key={index}\n                      onClick={(e) => handleExampleClick(example, e)}\n                      className=\"block w-full text-left text-xs text-blue-600 dark:text-blue-400 hover:underline truncate\"\n                    >\n                      {example}\n                    </button>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Notes */}\n            {site.notes && (\n              <div className=\"mt-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded\">\n                <p className=\"text-xs text-yellow-800 dark:text-yellow-200\">\n                  ⚠️ {site.notes}\n                </p>\n              </div>\n            )}\n          </div>\n        ))}\n      </div>\n\n      {filteredSites.length === 0 && (\n        <div className=\"text-center py-8 text-gray-500 dark:text-gray-400\">\n          <p>No sites found matching your criteria</p>\n          <p className=\"text-sm mt-1\">Try adjusting your search or category filter</p>\n        </div>\n      )}\n\n      {/* Footer Info */}\n      <div className=\"mt-6 pt-4 border-t border-gray-200 dark:border-gray-600\">\n        <div className=\"flex flex-wrap items-center justify-between text-sm text-gray-600 dark:text-gray-300\">\n          <div>\n            Showing {filteredSites.length} of {supportedSites.length} featured sites\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <span>📋 Playlists</span>\n            <span>🔴 Live Streams</span>\n            <span>📝 Subtitles</span>\n          </div>\n        </div>\n        <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-2\">\n          💡 This is a curated list of popular sites. yt-dlp supports {getTotalSitesCount()} total sites including many regional and specialized platforms.\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,cAAc,EAAE,YAAY,EAAE,eAAe,EAAsB;IACzF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,gBAAgB,6HAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,CAAA;QAC1C,MAAM,kBAAkB,qBAAqB,SAAS,KAAK,QAAQ,KAAK;QACxE,MAAM,gBAAgB,eAAe,MACnC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,KAAK,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEhE,OAAO,mBAAmB;IAC5B;IAEA,MAAM,qBAAqB,CAAC,KAAa;QACvC,EAAE,eAAe;QACjB,IAAI,iBAAiB;YACnB,gBAAgB;QAClB;IACF;IAEA,IAAI,CAAC,YAAY;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBACC,SAAS,IAAM,cAAc;gBAC7B,WAAU;;kCAEV,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,8OAAC;gCAAE,WAAU;;oCAA2C;oCAC9C,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD;oCAAI;;;;;;;;;;;;;kCAGjC,8OAAC;wBAAI,WAAU;wBAAwB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC/E,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;IAK/E;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,8OAAC;gCAAE,WAAU;;oCACV,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD;oCAAI;oCAA4B,6HAAA,CAAA,iBAAc,CAAC,MAAM;oCAAC;;;;;;;;;;;;;kCAG5E,8OAAC;wBACC,SAAS,IAAM,cAAc;wBAC7B,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAM3E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,WAAU;;;;;;;;;;;kCAGd,8OAAC;kCACC,cAAA,8OAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4BACnD,WAAU;;8CAEV,8OAAC;oCAAO,OAAM;8CAAM;;;;;;gCACnB,6HAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAA,yBAClB,8OAAC;wCAAsB,OAAO;kDAAW;uCAA5B;;;;;;;;;;;;;;;;;;;;;;0BAOrB,8OAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;wBAEC,WAAU;wBACV,SAAS,IAAM,eAAe;;0CAE9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAY,KAAK,IAAI;;;;;;kDACrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,KAAK,IAAI;;;;;;0DAEZ,8OAAC;gDAAE,WAAU;0DACV,KAAK,MAAM;;;;;;;;;;;;kDAGhB,8OAAC;wCAAK,WAAU;kDACb,KAAK,QAAQ;;;;;;;;;;;;0CAIlB,8OAAC;gCAAE,WAAU;0CACV,KAAK,WAAW;;;;;;0CAInB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;wCACZ,KAAK,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBAC9B,8OAAC;gDAEC,WAAU;0DAET;+CAHI;;;;;wCAMR,KAAK,QAAQ,CAAC,MAAM,GAAG,mBACtB,8OAAC;4CAAK,WAAU;;gDAA4F;gDACxG,KAAK,QAAQ,CAAC,MAAM,GAAG;gDAAE;;;;;;;;;;;;;;;;;;0CAOnC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CACZ,KAAK,iBAAiB,kBAAI,8OAAC;gDAAK,OAAM;0DAAqB;;;;;;4CAC3D,KAAK,YAAY,kBAAI,8OAAC;gDAAK,OAAM;0DAAwB;;;;;;4CACzD,KAAK,iBAAiB,kBAAI,8OAAC;gDAAK,OAAM;0DAAqB;;;;;;;;;;;;kDAE9D,8OAAC;wCAAK,WAAU;kDAAe,KAAK,UAAU;;;;;;;;;;;;4BAI/C,KAAK,QAAQ,CAAC,MAAM,GAAG,mBACtB,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAA4D;;;;;;kDAGzE,8OAAC;wCAAI,WAAU;kDACZ,KAAK,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBACvC,8OAAC;gDAEC,SAAS,CAAC,IAAM,mBAAmB,SAAS;gDAC5C,WAAU;0DAET;+CAJI;;;;;;;;;;;;;;;;4BAYd,KAAK,KAAK,kBACT,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;wCAA+C;wCACtD,KAAK,KAAK;;;;;;;;;;;;;uBA5Ef,KAAK,MAAM;;;;;;;;;;YAoFrB,cAAc,MAAM,KAAK,mBACxB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAE;;;;;;kCACH,8OAAC;wBAAE,WAAU;kCAAe;;;;;;;;;;;;0BAKhC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAI;oCACM,cAAc,MAAM;oCAAC;oCAAK,6HAAA,CAAA,iBAAc,CAAC,MAAM;oCAAC;;;;;;;0CAE3D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;kCAGV,8OAAC;wBAAE,WAAU;;4BAAgD;4BACE,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD;4BAAI;;;;;;;;;;;;;;;;;;;AAK5F", "debugId": null}}, {"offset": {"line": 4380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/URLValidator.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { findSiteByDomain, SiteInfo } from '@/data/supportedSites';\n\ninterface URLValidatorProps {\n  url: string;\n  onValidationChange?: (isValid: boolean, siteInfo: SiteInfo | null) => void;\n}\n\nexport default function URLValidator({ url, onValidationChange }: URLValidatorProps) {\n  const [siteInfo, setSiteInfo] = useState<SiteInfo | null>(null);\n  const [isValid, setIsValid] = useState(false);\n  const [isChecking, setIsChecking] = useState(false);\n  const [validationMessage, setValidationMessage] = useState('');\n\n  useEffect(() => {\n    if (!url.trim()) {\n      setSiteInfo(null);\n      setIsValid(false);\n      setValidationMessage('');\n      onValidationChange?.(false, null);\n      return;\n    }\n\n    validateURL(url);\n  }, [url]);\n\n  const validateURL = async (inputUrl: string) => {\n    setIsChecking(true);\n    \n    try {\n      // Basic URL validation\n      const urlObj = new URL(inputUrl);\n      \n      // Find site info\n      const detectedSite = findSiteByDomain(inputUrl);\n      \n      if (detectedSite) {\n        setSiteInfo(detectedSite);\n        setIsValid(true);\n        setValidationMessage(`✅ Supported ${detectedSite.category} platform`);\n        onValidationChange?.(true, detectedSite);\n      } else {\n        // Check if it might still be supported (yt-dlp supports many unlisted sites)\n        setSiteInfo(null);\n        setIsValid(true); // Assume it might work\n        setValidationMessage('⚠️ Site not in our directory, but may still be supported');\n        onValidationChange?.(true, null);\n      }\n    } catch (error) {\n      setSiteInfo(null);\n      setIsValid(false);\n      setValidationMessage('❌ Invalid URL format');\n      onValidationChange?.(false, null);\n    } finally {\n      setIsChecking(false);\n    }\n  };\n\n  if (!url.trim()) {\n    return null;\n  }\n\n  return (\n    <div className=\"mt-2\">\n      {isChecking ? (\n        <div className=\"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300\">\n          <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"></div>\n          <span>Checking URL...</span>\n        </div>\n      ) : (\n        <div className=\"space-y-2\">\n          {/* Validation Status */}\n          <div className={`text-sm ${\n            isValid \n              ? siteInfo \n                ? 'text-green-600 dark:text-green-400' \n                : 'text-yellow-600 dark:text-yellow-400'\n              : 'text-red-600 dark:text-red-400'\n          }`}>\n            {validationMessage}\n          </div>\n\n          {/* Site Information Card */}\n          {siteInfo && (\n            <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3\">\n              <div className=\"flex items-center space-x-3 mb-2\">\n                <span className=\"text-lg\">{siteInfo.icon}</span>\n                <div className=\"flex-1\">\n                  <h4 className=\"text-sm font-medium text-blue-800 dark:text-blue-200\">\n                    {siteInfo.name}\n                  </h4>\n                  <p className=\"text-xs text-blue-600 dark:text-blue-300\">\n                    {siteInfo.description}\n                  </p>\n                </div>\n                <span className=\"px-2 py-1 text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded\">\n                  {siteInfo.category}\n                </span>\n              </div>\n\n              {/* Capabilities */}\n              <div className=\"grid grid-cols-2 gap-2 text-xs\">\n                <div className=\"flex items-center space-x-1\">\n                  <span>📺</span>\n                  <span className=\"text-blue-700 dark:text-blue-300\">\n                    Max Quality: {siteInfo.maxQuality}\n                  </span>\n                </div>\n                <div className=\"flex items-center space-x-1\">\n                  <span>🎵</span>\n                  <span className=\"text-blue-700 dark:text-blue-300\">\n                    Audio: {siteInfo.audioFormats.join(', ')}\n                  </span>\n                </div>\n                {siteInfo.supportsPlaylists && (\n                  <div className=\"flex items-center space-x-1\">\n                    <span>📋</span>\n                    <span className=\"text-blue-700 dark:text-blue-300\">Playlists</span>\n                  </div>\n                )}\n                {siteInfo.supportsLive && (\n                  <div className=\"flex items-center space-x-1\">\n                    <span>🔴</span>\n                    <span className=\"text-blue-700 dark:text-blue-300\">Live Streams</span>\n                  </div>\n                )}\n                {siteInfo.supportsSubtitles && (\n                  <div className=\"flex items-center space-x-1\">\n                    <span>📝</span>\n                    <span className=\"text-blue-700 dark:text-blue-300\">Subtitles</span>\n                  </div>\n                )}\n              </div>\n\n              {/* Features */}\n              <div className=\"mt-2\">\n                <div className=\"flex flex-wrap gap-1\">\n                  {siteInfo.features.slice(0, 4).map((feature) => (\n                    <span\n                      key={feature}\n                      className=\"px-2 py-0.5 text-xs bg-blue-100 dark:bg-blue-800 text-blue-700 dark:text-blue-200 rounded\"\n                    >\n                      {feature}\n                    </span>\n                  ))}\n                  {siteInfo.features.length > 4 && (\n                    <span className=\"px-2 py-0.5 text-xs bg-blue-100 dark:bg-blue-800 text-blue-700 dark:text-blue-200 rounded\">\n                      +{siteInfo.features.length - 4} more\n                    </span>\n                  )}\n                </div>\n              </div>\n\n              {/* Notes/Warnings */}\n              {siteInfo.notes && (\n                <div className=\"mt-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded\">\n                  <p className=\"text-xs text-yellow-800 dark:text-yellow-200\">\n                    ⚠️ {siteInfo.notes}\n                  </p>\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* Unknown Site Info */}\n          {!siteInfo && isValid && (\n            <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <span className=\"text-lg\">🌐</span>\n                <h4 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200\">\n                  Unknown Site\n                </h4>\n              </div>\n              <p className=\"text-xs text-yellow-700 dark:text-yellow-300 mb-2\">\n                This site is not in our featured directory, but yt-dlp supports over 1000+ sites. \n                It may still work perfectly!\n              </p>\n              <div className=\"text-xs text-yellow-600 dark:text-yellow-400\">\n                💡 Try the \"Analyze\" button to check if this site is supported\n              </div>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,aAAa,EAAE,GAAG,EAAE,kBAAkB,EAAqB;IACjF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,IAAI,IAAI,IAAI;YACf,YAAY;YACZ,WAAW;YACX,qBAAqB;YACrB,qBAAqB,OAAO;YAC5B;QACF;QAEA,YAAY;IACd,GAAG;QAAC;KAAI;IAER,MAAM,cAAc,OAAO;QACzB,cAAc;QAEd,IAAI;YACF,uBAAuB;YACvB,MAAM,SAAS,IAAI,IAAI;YAEvB,iBAAiB;YACjB,MAAM,eAAe,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD,EAAE;YAEtC,IAAI,cAAc;gBAChB,YAAY;gBACZ,WAAW;gBACX,qBAAqB,CAAC,YAAY,EAAE,aAAa,QAAQ,CAAC,SAAS,CAAC;gBACpE,qBAAqB,MAAM;YAC7B,OAAO;gBACL,6EAA6E;gBAC7E,YAAY;gBACZ,WAAW,OAAO,uBAAuB;gBACzC,qBAAqB;gBACrB,qBAAqB,MAAM;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,YAAY;YACZ,WAAW;YACX,qBAAqB;YACrB,qBAAqB,OAAO;QAC9B,SAAU;YACR,cAAc;QAChB;IACF;IAEA,IAAI,CAAC,IAAI,IAAI,IAAI;QACf,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,2BACC,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;8BAAK;;;;;;;;;;;iCAGR,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAW,CAAC,QAAQ,EACvB,UACI,WACE,uCACA,yCACF,kCACJ;8BACC;;;;;;gBAIF,0BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAW,SAAS,IAAI;;;;;;8CACxC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,SAAS,IAAI;;;;;;sDAEhB,8OAAC;4CAAE,WAAU;sDACV,SAAS,WAAW;;;;;;;;;;;;8CAGzB,8OAAC;oCAAK,WAAU;8CACb,SAAS,QAAQ;;;;;;;;;;;;sCAKtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;4CAAK,WAAU;;gDAAmC;gDACnC,SAAS,UAAU;;;;;;;;;;;;;8CAGrC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;4CAAK,WAAU;;gDAAmC;gDACzC,SAAS,YAAY,CAAC,IAAI,CAAC;;;;;;;;;;;;;gCAGtC,SAAS,iBAAiB,kBACzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;4CAAK,WAAU;sDAAmC;;;;;;;;;;;;gCAGtD,SAAS,YAAY,kBACpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;4CAAK,WAAU;sDAAmC;;;;;;;;;;;;gCAGtD,SAAS,iBAAiB,kBACzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;4CAAK,WAAU;sDAAmC;;;;;;;;;;;;;;;;;;sCAMzD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;oCACZ,SAAS,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBAClC,8OAAC;4CAEC,WAAU;sDAET;2CAHI;;;;;oCAMR,SAAS,QAAQ,CAAC,MAAM,GAAG,mBAC1B,8OAAC;wCAAK,WAAU;;4CAA4F;4CACxG,SAAS,QAAQ,CAAC,MAAM,GAAG;4CAAE;;;;;;;;;;;;;;;;;;wBAOtC,SAAS,KAAK,kBACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAA+C;oCACtD,SAAS,KAAK;;;;;;;;;;;;;;;;;;gBAQ3B,CAAC,YAAY,yBACZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAG,WAAU;8CAA2D;;;;;;;;;;;;sCAI3E,8OAAC;4BAAE,WAAU;sCAAoD;;;;;;sCAIjE,8OAAC;4BAAI,WAAU;sCAA+C;;;;;;;;;;;;;;;;;;;;;;;AAS5E", "debugId": null}}, {"offset": {"line": 4789, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/PlatformSpecificOptions.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { SiteInfo } from '@/data/supportedSites';\n\ninterface PlatformSpecificOptionsProps {\n  siteInfo: SiteInfo | null;\n  onOptionsChange: (options: any) => void;\n}\n\nexport default function PlatformSpecificOptions({ siteInfo, onOptionsChange }: PlatformSpecificOptionsProps) {\n  const [options, setOptions] = useState<any>({});\n\n  const updateOption = (key: string, value: any) => {\n    const newOptions = { ...options, [key]: value };\n    setOptions(newOptions);\n    onOptionsChange(newOptions);\n  };\n\n  if (!siteInfo) {\n    return null;\n  }\n\n  const renderYouTubeOptions = () => (\n    <div className=\"space-y-4\">\n      <h4 className=\"text-sm font-medium text-gray-800 dark:text-white\">\n        🎥 YouTube Specific Options\n      </h4>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <label className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            checked={options.writeDescription || false}\n            onChange={(e) => updateOption('writeDescription', e.target.checked)}\n            className=\"mr-2\"\n          />\n          <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n            Save video description\n          </span>\n        </label>\n\n        <label className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            checked={options.writeComments || false}\n            onChange={(e) => updateOption('writeComments', e.target.checked)}\n            className=\"mr-2\"\n          />\n          <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n            Download comments\n          </span>\n        </label>\n\n        <label className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            checked={options.writeThumbnail || false}\n            onChange={(e) => updateOption('writeThumbnail', e.target.checked)}\n            className=\"mr-2\"\n          />\n          <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n            Save thumbnail\n          </span>\n        </label>\n\n        <label className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            checked={options.writeAnnotations || false}\n            onChange={(e) => updateOption('writeAnnotations', e.target.checked)}\n            className=\"mr-2\"\n          />\n          <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n            Save annotations\n          </span>\n        </label>\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Playlist Options\n        </label>\n        <select\n          value={options.playlistItems || 'all'}\n          onChange={(e) => updateOption('playlistItems', e.target.value)}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white\"\n        >\n          <option value=\"all\">Download entire playlist</option>\n          <option value=\"1-10\">First 10 videos</option>\n          <option value=\"1-25\">First 25 videos</option>\n          <option value=\"1-50\">First 50 videos</option>\n          <option value=\"custom\">Custom range</option>\n        </select>\n      </div>\n    </div>\n  );\n\n  const renderTikTokOptions = () => (\n    <div className=\"space-y-4\">\n      <h4 className=\"text-sm font-medium text-gray-800 dark:text-white\">\n        🎵 TikTok Specific Options\n      </h4>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <label className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            checked={options.noWatermark || false}\n            onChange={(e) => updateOption('noWatermark', e.target.checked)}\n            className=\"mr-2\"\n          />\n          <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n            Remove watermark (if possible)\n          </span>\n        </label>\n\n        <label className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            checked={options.downloadMusic || false}\n            onChange={(e) => updateOption('downloadMusic', e.target.checked)}\n            className=\"mr-2\"\n          />\n          <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n            Extract background music\n          </span>\n        </label>\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          User Profile Downloads\n        </label>\n        <select\n          value={options.userVideos || 'recent'}\n          onChange={(e) => updateOption('userVideos', e.target.value)}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white\"\n        >\n          <option value=\"recent\">Recent videos only</option>\n          <option value=\"all\">All public videos</option>\n          <option value=\"liked\">Liked videos (if accessible)</option>\n        </select>\n      </div>\n    </div>\n  );\n\n  const renderInstagramOptions = () => (\n    <div className=\"space-y-4\">\n      <h4 className=\"text-sm font-medium text-gray-800 dark:text-white\">\n        📸 Instagram Specific Options\n      </h4>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <label className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            checked={options.downloadStories || false}\n            onChange={(e) => updateOption('downloadStories', e.target.checked)}\n            className=\"mr-2\"\n          />\n          <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n            Download stories\n          </span>\n        </label>\n\n        <label className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            checked={options.downloadReels || false}\n            onChange={(e) => updateOption('downloadReels', e.target.checked)}\n            className=\"mr-2\"\n          />\n          <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n            Download reels\n          </span>\n        </label>\n\n        <label className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            checked={options.downloadIGTV || false}\n            onChange={(e) => updateOption('downloadIGTV', e.target.checked)}\n            className=\"mr-2\"\n          />\n          <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n            Download IGTV\n          </span>\n        </label>\n\n        <label className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            checked={options.highestQuality || false}\n            onChange={(e) => updateOption('highestQuality', e.target.checked)}\n            className=\"mr-2\"\n          />\n          <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n            Highest quality available\n          </span>\n        </label>\n      </div>\n    </div>\n  );\n\n  const renderTwitchOptions = () => (\n    <div className=\"space-y-4\">\n      <h4 className=\"text-sm font-medium text-gray-800 dark:text-white\">\n        🎮 Twitch Specific Options\n      </h4>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <label className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            checked={options.downloadChat || false}\n            onChange={(e) => updateOption('downloadChat', e.target.checked)}\n            className=\"mr-2\"\n          />\n          <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n            Download chat replay\n          </span>\n        </label>\n\n        <label className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            checked={options.skipAds || false}\n            onChange={(e) => updateOption('skipAds', e.target.checked)}\n            className=\"mr-2\"\n          />\n          <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n            Skip advertisements\n          </span>\n        </label>\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          VOD Quality Preference\n        </label>\n        <select\n          value={options.twitchQuality || 'best'}\n          onChange={(e) => updateOption('twitchQuality', e.target.value)}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white\"\n        >\n          <option value=\"best\">Best available</option>\n          <option value=\"source\">Source quality</option>\n          <option value=\"720p60\">720p 60fps</option>\n          <option value=\"720p30\">720p 30fps</option>\n          <option value=\"480p30\">480p 30fps</option>\n        </select>\n      </div>\n    </div>\n  );\n\n  const renderTwitterOptions = () => (\n    <div className=\"space-y-4\">\n      <h4 className=\"text-sm font-medium text-gray-800 dark:text-white\">\n        🐦 Twitter/X Specific Options\n      </h4>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <label className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            checked={options.downloadSpaces || false}\n            onChange={(e) => updateOption('downloadSpaces', e.target.checked)}\n            className=\"mr-2\"\n          />\n          <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n            Download Spaces audio\n          </span>\n        </label>\n\n        <label className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            checked={options.includeReplies || false}\n            onChange={(e) => updateOption('includeReplies', e.target.checked)}\n            className=\"mr-2\"\n          />\n          <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n            Include reply videos\n          </span>\n        </label>\n      </div>\n    </div>\n  );\n\n  const renderGenericOptions = () => (\n    <div className=\"space-y-4\">\n      <h4 className=\"text-sm font-medium text-gray-800 dark:text-white\">\n        🌐 General Platform Options\n      </h4>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <label className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            checked={options.writeMetadata || false}\n            onChange={(e) => updateOption('writeMetadata', e.target.checked)}\n            className=\"mr-2\"\n          />\n          <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n            Save metadata\n          </span>\n        </label>\n\n        <label className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            checked={options.embedSubs || false}\n            onChange={(e) => updateOption('embedSubs', e.target.checked)}\n            className=\"mr-2\"\n          />\n          <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n            Embed subtitles\n          </span>\n        </label>\n      </div>\n    </div>\n  );\n\n  const renderPlatformOptions = () => {\n    switch (siteInfo.domain) {\n      case 'youtube.com':\n        return renderYouTubeOptions();\n      case 'tiktok.com':\n        return renderTikTokOptions();\n      case 'instagram.com':\n        return renderInstagramOptions();\n      case 'twitch.tv':\n        return renderTwitchOptions();\n      case 'twitter.com':\n        return renderTwitterOptions();\n      default:\n        return renderGenericOptions();\n    }\n  };\n\n  return (\n    <div className=\"mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n      {renderPlatformOptions()}\n      \n      {/* Platform-specific tips */}\n      <div className=\"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded\">\n        <h5 className=\"text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">\n          💡 Platform Tips\n        </h5>\n        <div className=\"text-xs text-blue-700 dark:text-blue-300\">\n          {siteInfo.domain === 'youtube.com' && (\n            <p>• Use playlist URLs to download entire playlists • Comments may take longer to process</p>\n          )}\n          {siteInfo.domain === 'tiktok.com' && (\n            <p>• Some features may require account access • Watermark removal depends on source</p>\n          )}\n          {siteInfo.domain === 'instagram.com' && (\n            <p>• Private accounts require authentication • Stories expire after 24 hours</p>\n          )}\n          {siteInfo.domain === 'twitch.tv' && (\n            <p>• VODs may have limited availability • Chat replay adds significant file size</p>\n          )}\n          {siteInfo.domain === 'twitter.com' && (\n            <p>• Some content may require authentication • Spaces are audio-only</p>\n          )}\n          {!['youtube.com', 'tiktok.com', 'instagram.com', 'twitch.tv', 'twitter.com'].includes(siteInfo.domain) && (\n            <p>• Platform-specific features may vary • Check site documentation for details</p>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUe,SAAS,wBAAwB,EAAE,QAAQ,EAAE,eAAe,EAAgC;IACzG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IAE7C,MAAM,eAAe,CAAC,KAAa;QACjC,MAAM,aAAa;YAAE,GAAG,OAAO;YAAE,CAAC,IAAI,EAAE;QAAM;QAC9C,WAAW;QACX,gBAAgB;IAClB;IAEA,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IAEA,MAAM,uBAAuB,kBAC3B,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAoD;;;;;;8BAIlE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,QAAQ,gBAAgB,IAAI;oCACrC,UAAU,CAAC,IAAM,aAAa,oBAAoB,EAAE,MAAM,CAAC,OAAO;oCAClE,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAK7D,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,QAAQ,aAAa,IAAI;oCAClC,UAAU,CAAC,IAAM,aAAa,iBAAiB,EAAE,MAAM,CAAC,OAAO;oCAC/D,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAK7D,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,QAAQ,cAAc,IAAI;oCACnC,UAAU,CAAC,IAAM,aAAa,kBAAkB,EAAE,MAAM,CAAC,OAAO;oCAChE,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAK7D,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,QAAQ,gBAAgB,IAAI;oCACrC,UAAU,CAAC,IAAM,aAAa,oBAAoB,EAAE,MAAM,CAAC,OAAO;oCAClE,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;;;;;;;8BAM/D,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAAkE;;;;;;sCAGnF,8OAAC;4BACC,OAAO,QAAQ,aAAa,IAAI;4BAChC,UAAU,CAAC,IAAM,aAAa,iBAAiB,EAAE,MAAM,CAAC,KAAK;4BAC7D,WAAU;;8CAEV,8OAAC;oCAAO,OAAM;8CAAM;;;;;;8CACpB,8OAAC;oCAAO,OAAM;8CAAO;;;;;;8CACrB,8OAAC;oCAAO,OAAM;8CAAO;;;;;;8CACrB,8OAAC;oCAAO,OAAM;8CAAO;;;;;;8CACrB,8OAAC;oCAAO,OAAM;8CAAS;;;;;;;;;;;;;;;;;;;;;;;;IAM/B,MAAM,sBAAsB,kBAC1B,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAoD;;;;;;8BAIlE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,QAAQ,WAAW,IAAI;oCAChC,UAAU,CAAC,IAAM,aAAa,eAAe,EAAE,MAAM,CAAC,OAAO;oCAC7D,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAK7D,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,QAAQ,aAAa,IAAI;oCAClC,UAAU,CAAC,IAAM,aAAa,iBAAiB,EAAE,MAAM,CAAC,OAAO;oCAC/D,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;;;;;;;8BAM/D,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAAkE;;;;;;sCAGnF,8OAAC;4BACC,OAAO,QAAQ,UAAU,IAAI;4BAC7B,UAAU,CAAC,IAAM,aAAa,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC1D,WAAU;;8CAEV,8OAAC;oCAAO,OAAM;8CAAS;;;;;;8CACvB,8OAAC;oCAAO,OAAM;8CAAM;;;;;;8CACpB,8OAAC;oCAAO,OAAM;8CAAQ;;;;;;;;;;;;;;;;;;;;;;;;IAM9B,MAAM,yBAAyB,kBAC7B,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAoD;;;;;;8BAIlE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,QAAQ,eAAe,IAAI;oCACpC,UAAU,CAAC,IAAM,aAAa,mBAAmB,EAAE,MAAM,CAAC,OAAO;oCACjE,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAK7D,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,QAAQ,aAAa,IAAI;oCAClC,UAAU,CAAC,IAAM,aAAa,iBAAiB,EAAE,MAAM,CAAC,OAAO;oCAC/D,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAK7D,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,QAAQ,YAAY,IAAI;oCACjC,UAAU,CAAC,IAAM,aAAa,gBAAgB,EAAE,MAAM,CAAC,OAAO;oCAC9D,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAK7D,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,QAAQ,cAAc,IAAI;oCACnC,UAAU,CAAC,IAAM,aAAa,kBAAkB,EAAE,MAAM,CAAC,OAAO;oCAChE,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;;;;;;;;;;;;;IAQnE,MAAM,sBAAsB,kBAC1B,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAoD;;;;;;8BAIlE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,QAAQ,YAAY,IAAI;oCACjC,UAAU,CAAC,IAAM,aAAa,gBAAgB,EAAE,MAAM,CAAC,OAAO;oCAC9D,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAK7D,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,QAAQ,OAAO,IAAI;oCAC5B,UAAU,CAAC,IAAM,aAAa,WAAW,EAAE,MAAM,CAAC,OAAO;oCACzD,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;;;;;;;8BAM/D,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAAkE;;;;;;sCAGnF,8OAAC;4BACC,OAAO,QAAQ,aAAa,IAAI;4BAChC,UAAU,CAAC,IAAM,aAAa,iBAAiB,EAAE,MAAM,CAAC,KAAK;4BAC7D,WAAU;;8CAEV,8OAAC;oCAAO,OAAM;8CAAO;;;;;;8CACrB,8OAAC;oCAAO,OAAM;8CAAS;;;;;;8CACvB,8OAAC;oCAAO,OAAM;8CAAS;;;;;;8CACvB,8OAAC;oCAAO,OAAM;8CAAS;;;;;;8CACvB,8OAAC;oCAAO,OAAM;8CAAS;;;;;;;;;;;;;;;;;;;;;;;;IAM/B,MAAM,uBAAuB,kBAC3B,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAoD;;;;;;8BAIlE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,QAAQ,cAAc,IAAI;oCACnC,UAAU,CAAC,IAAM,aAAa,kBAAkB,EAAE,MAAM,CAAC,OAAO;oCAChE,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAK7D,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,QAAQ,cAAc,IAAI;oCACnC,UAAU,CAAC,IAAM,aAAa,kBAAkB,EAAE,MAAM,CAAC,OAAO;oCAChE,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;;;;;;;;;;;;;IAQnE,MAAM,uBAAuB,kBAC3B,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAoD;;;;;;8BAIlE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,QAAQ,aAAa,IAAI;oCAClC,UAAU,CAAC,IAAM,aAAa,iBAAiB,EAAE,MAAM,CAAC,OAAO;oCAC/D,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAK7D,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,QAAQ,SAAS,IAAI;oCAC9B,UAAU,CAAC,IAAM,aAAa,aAAa,EAAE,MAAM,CAAC,OAAO;oCAC3D,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;;;;;;;;;;;;;IAQnE,MAAM,wBAAwB;QAC5B,OAAQ,SAAS,MAAM;YACrB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ;0BAGD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAG1E,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,MAAM,KAAK,+BACnB,8OAAC;0CAAE;;;;;;4BAEJ,SAAS,MAAM,KAAK,8BACnB,8OAAC;0CAAE;;;;;;4BAEJ,SAAS,MAAM,KAAK,iCACnB,8OAAC;0CAAE;;;;;;4BAEJ,SAAS,MAAM,KAAK,6BACnB,8OAAC;0CAAE;;;;;;4BAEJ,SAAS,MAAM,KAAK,+BACnB,8OAAC;0CAAE;;;;;;4BAEJ,CAAC;gCAAC;gCAAe;gCAAc;gCAAiB;gCAAa;6BAAc,CAAC,QAAQ,CAAC,SAAS,MAAM,mBACnG,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 5690, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/SetupGuide.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nexport default function SetupGuide() {\n  const [currentStep, setCurrentStep] = useState(1);\n  const [isExpanded, setIsExpanded] = useState(false);\n\n  const steps = [\n    {\n      title: \"Create Google Cloud Project\",\n      description: \"Set up a new project in Google Cloud Console\",\n      details: [\n        \"Go to https://console.cloud.google.com/\",\n        \"Click 'Select a project' → 'New Project'\",\n        \"Name: 'Video Downloader App'\",\n        \"Click 'Create'\"\n      ]\n    },\n    {\n      title: \"Enable YouTube Data API\",\n      description: \"Enable the YouTube Data API v3 for your project\",\n      details: [\n        \"Navigate to 'APIs & Services' → 'Library'\",\n        \"Search for 'YouTube Data API v3'\",\n        \"Click on it and then 'Enable'\"\n      ]\n    },\n    {\n      title: \"Configure OAuth Consent Screen\",\n      description: \"Set up the OAuth consent screen for your application\",\n      details: [\n        \"Go to 'APIs & Services' → 'OAuth consent screen'\",\n        \"User Type: External\",\n        \"App name: 'Video Downloader'\",\n        \"User support email: Your email\",\n        \"Add scopes: youtube.readonly, youtube.force-ssl\"\n      ]\n    },\n    {\n      title: \"Create OAuth2 Credentials\",\n      description: \"Generate OAuth2 client ID and secret\",\n      details: [\n        \"Go to 'APIs & Services' → 'Credentials'\",\n        \"Click 'Create Credentials' → 'OAuth client ID'\",\n        \"Application type: Web application\",\n        \"Authorized redirect URIs:\",\n        \"  • http://localhost:3000/api/auth/callback/google\",\n        \"Copy the Client ID and Client Secret\"\n      ]\n    },\n    {\n      title: \"Update Environment Variables\",\n      description: \"Add your credentials to the .env.local file\",\n      details: [\n        \"Open video-downloader/.env.local\",\n        \"Replace GOOGLE_CLIENT_ID with your actual Client ID\",\n        \"Replace GOOGLE_CLIENT_SECRET with your actual Client Secret\",\n        \"Generate a secure NEXTAUTH_SECRET (random string)\",\n        \"Restart the development server\"\n      ]\n    }\n  ];\n\n  if (!isExpanded) {\n    return (\n      <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 bg-yellow-100 dark:bg-yellow-800 rounded-full flex items-center justify-center\">\n              <svg className=\"w-4 h-4 text-yellow-600 dark:text-yellow-200\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n              </svg>\n            </div>\n            <div>\n              <h3 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200\">\n                ⚙️ YouTube Authentication Setup Required\n              </h3>\n              <p className=\"text-xs text-yellow-700 dark:text-yellow-300\">\n                OAuth credentials need to be configured to enable YouTube login\n              </p>\n            </div>\n          </div>\n          <button\n            onClick={() => setIsExpanded(true)}\n            className=\"px-3 py-1 text-xs bg-yellow-600 text-white rounded hover:bg-yellow-700 transition-colors\"\n          >\n            Setup Guide\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white\">\n          🔧 YouTube Authentication Setup Guide\n        </h3>\n        <button\n          onClick={() => setIsExpanded(false)}\n          className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-200\"\n        >\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n          </svg>\n        </button>\n      </div>\n\n      <div className=\"space-y-6\">\n        {/* Progress Indicator */}\n        <div className=\"flex items-center space-x-2 mb-6\">\n          {steps.map((_, index) => (\n            <div key={index} className=\"flex items-center\">\n              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${\n                index + 1 <= currentStep \n                  ? 'bg-blue-600 text-white' \n                  : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300'\n              }`}>\n                {index + 1}\n              </div>\n              {index < steps.length - 1 && (\n                <div className={`w-8 h-0.5 ${\n                  index + 1 < currentStep ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-600'\n                }`} />\n              )}\n            </div>\n          ))}\n        </div>\n\n        {/* Current Step */}\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6\">\n          <h4 className=\"text-lg font-medium text-blue-800 dark:text-blue-200 mb-2\">\n            Step {currentStep}: {steps[currentStep - 1].title}\n          </h4>\n          <p className=\"text-blue-700 dark:text-blue-300 mb-4\">\n            {steps[currentStep - 1].description}\n          </p>\n          \n          <div className=\"space-y-2\">\n            {steps[currentStep - 1].details.map((detail, index) => (\n              <div key={index} className=\"flex items-start space-x-2\">\n                <div className=\"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0\" />\n                <p className=\"text-sm text-blue-800 dark:text-blue-200 font-mono\">\n                  {detail}\n                </p>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Navigation */}\n        <div className=\"flex items-center justify-between\">\n          <button\n            onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}\n            disabled={currentStep === 1}\n            className=\"px-4 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            Previous\n          </button>\n          \n          <span className=\"text-sm text-gray-600 dark:text-gray-300\">\n            Step {currentStep} of {steps.length}\n          </span>\n          \n          <button\n            onClick={() => setCurrentStep(Math.min(steps.length, currentStep + 1))}\n            disabled={currentStep === steps.length}\n            className=\"px-4 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            Next\n          </button>\n        </div>\n\n        {/* Quick Links */}\n        <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n          <h5 className=\"text-sm font-medium text-gray-800 dark:text-white mb-3\">\n            🔗 Quick Links\n          </h5>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm\">\n            <a\n              href=\"https://console.cloud.google.com/\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-blue-600 dark:text-blue-400 hover:underline\"\n            >\n              → Google Cloud Console\n            </a>\n            <a\n              href=\"https://console.cloud.google.com/apis/library/youtube.googleapis.com\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-blue-600 dark:text-blue-400 hover:underline\"\n            >\n              → YouTube Data API v3\n            </a>\n            <a\n              href=\"https://console.cloud.google.com/apis/credentials\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-blue-600 dark:text-blue-400 hover:underline\"\n            >\n              → API Credentials\n            </a>\n            <a\n              href=\"https://next-auth.js.org/providers/google\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-blue-600 dark:text-blue-400 hover:underline\"\n            >\n              → NextAuth Google Provider Docs\n            </a>\n          </div>\n        </div>\n\n        {/* Environment Variables Template */}\n        {currentStep === 5 && (\n          <div className=\"bg-gray-900 rounded-lg p-4\">\n            <h5 className=\"text-sm font-medium text-white mb-3\">\n              📝 .env.local Template\n            </h5>\n            <pre className=\"text-xs text-green-400 overflow-x-auto\">\n{`# Replace these with your actual values from Google Cloud Console\nGOOGLE_CLIENT_ID=123456789-abcdefghijklmnop.apps.googleusercontent.com\nGOOGLE_CLIENT_SECRET=GOCSPX-your_actual_client_secret_here\n\n# NextAuth Configuration\nNEXTAUTH_URL=http://localhost:3000\nNEXTAUTH_SECRET=your_secure_random_secret_key_here\n\n# Optional: YouTube API Key for public data\nYOUTUBE_API_KEY=your_youtube_api_key_here`}\n            </pre>\n          </div>\n        )}\n\n        {/* Completion Message */}\n        {currentStep === steps.length && (\n          <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <svg className=\"w-5 h-5 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n              </svg>\n              <h5 className=\"text-sm font-medium text-green-800 dark:text-green-200\">\n                Setup Complete!\n              </h5>\n            </div>\n            <p className=\"text-xs text-green-700 dark:text-green-300 mt-2\">\n              After updating your .env.local file and restarting the server, you'll be able to sign in with YouTube and access your purchased content.\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,aAAa;YACb,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,aAAa;YACb,SAAS;gBACP;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,aAAa;YACb,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,aAAa;YACb,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,aAAa;YACb,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;QACH;KACD;IAED,IAAI,CAAC,YAAY;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAA+C,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACtG,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAGzE,8OAAC;wCAAE,WAAU;kDAA+C;;;;;;;;;;;;;;;;;;kCAKhE,8OAAC;wBACC,SAAS,IAAM,cAAc;wBAC7B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsD;;;;;;kCAGpE,8OAAC;wBACC,SAAS,IAAM,cAAc;wBAC7B,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAK3E,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,GAAG,sBACb,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAI,WAAW,CAAC,0EAA0E,EACzF,QAAQ,KAAK,cACT,2BACA,iEACJ;kDACC,QAAQ;;;;;;oCAEV,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;wCAAI,WAAW,CAAC,UAAU,EACzB,QAAQ,IAAI,cAAc,gBAAgB,gCAC1C;;;;;;;+BAXI;;;;;;;;;;kCAkBd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAA4D;oCAClE;oCAAY;oCAAG,KAAK,CAAC,cAAc,EAAE,CAAC,KAAK;;;;;;;0CAEnD,8OAAC;gCAAE,WAAU;0CACV,KAAK,CAAC,cAAc,EAAE,CAAC,WAAW;;;;;;0CAGrC,8OAAC;gCAAI,WAAU;0CACZ,KAAK,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC3C,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAE,WAAU;0DACV;;;;;;;uCAHK;;;;;;;;;;;;;;;;kCAWhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;gCACxD,UAAU,gBAAgB;gCAC1B,WAAU;0CACX;;;;;;0CAID,8OAAC;gCAAK,WAAU;;oCAA2C;oCACnD;oCAAY;oCAAK,MAAM,MAAM;;;;;;;0CAGrC,8OAAC;gCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,cAAc;gCACnE,UAAU,gBAAgB,MAAM,MAAM;gCACtC,WAAU;0CACX;;;;;;;;;;;;kCAMH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAGvE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDACX;;;;;;;;;;;;;;;;;;oBAOJ,gBAAgB,mBACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAI,WAAU;0CAC1B,CAAC;;;;;;;;;yCASuC,CAAC;;;;;;;;;;;;oBAMjC,gBAAgB,MAAM,MAAM,kBAC3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAA6C,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACpG,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,8OAAC;wCAAG,WAAU;kDAAyD;;;;;;;;;;;;0CAIzE,8OAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;;;;;;;;;;;;;AAQ3E", "debugId": null}}, {"offset": {"line": 6205, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/YouTubeAuth.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { signIn, signOut, useSession } from 'next-auth/react';\nimport SetupGuide from './SetupGuide';\n\ninterface YouTubeAuthProps {\n  onAuthChange?: (isAuthenticated: boolean, userData?: any) => void;\n}\n\nexport default function YouTubeAuth({ onAuthChange }: YouTubeAuthProps) {\n  const { data: session, status } = useSession();\n  const [userContent, setUserContent] = useState<any>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    if (session?.accessToken) {\n      onAuthChange?.(true, session.user);\n      fetchUserContent();\n    } else {\n      onAuthChange?.(false);\n      setUserContent(null);\n    }\n  }, [session]);\n\n  const fetchUserContent = async () => {\n    if (!session?.accessToken) return;\n\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await fetch('/api/youtube/purchased');\n      const data = await response.json();\n\n      if (!response.ok) {\n        if (data.requiresReauth) {\n          setError('Please re-authenticate to access your YouTube content');\n          return;\n        }\n        throw new Error(data.message || 'Failed to fetch content');\n      }\n\n      setUserContent(data.data);\n    } catch (err: any) {\n      setError(err.message || 'Failed to load your YouTube content');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSignIn = () => {\n    signIn('google', {\n      callbackUrl: window.location.href,\n    });\n  };\n\n  // Check if we're in development and credentials might not be set up\n  const isDevelopment = process.env.NODE_ENV === 'development';\n  const hasOAuthError = error.includes('OAuth') || error.includes('invalid_client');\n\n  const handleSignOut = () => {\n    signOut({\n      callbackUrl: window.location.href,\n    });\n    setUserContent(null);\n  };\n\n  if (status === 'loading') {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"></div>\n          <span className=\"text-gray-600 dark:text-gray-300\">Loading authentication...</span>\n        </div>\n      </div>\n    );\n  }\n\n  if (!session) {\n    return (\n      <div className=\"space-y-6\">\n        {/* Show setup guide if there's an OAuth error */}\n        {hasOAuthError && <SetupGuide />}\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n          <div className=\"text-center\">\n          <div className=\"mb-4\">\n            <div className=\"mx-auto w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4\">\n              <svg className=\"w-8 h-8 text-red-600 dark:text-red-400\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z\"/>\n              </svg>\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white mb-2\">\n              🔐 YouTube Authentication\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-300 mb-4\">\n              Sign in with your YouTube account to access and download your purchased content, private videos, and playlists.\n            </p>\n          </div>\n\n          <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6\">\n            <h4 className=\"text-sm font-medium text-blue-800 dark:text-blue-200 mb-2\">\n              🎯 What you can access:\n            </h4>\n            <ul className=\"text-sm text-blue-700 dark:text-blue-300 space-y-1\">\n              <li>• Your purchased movies and TV shows</li>\n              <li>• Private videos you've uploaded</li>\n              <li>• Your personal playlists</li>\n              <li>• Channel content you own</li>\n              <li>• Premium quality downloads</li>\n            </ul>\n          </div>\n\n          <button\n            onClick={handleSignIn}\n            className=\"w-full px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors flex items-center justify-center space-x-2\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>\n              <path d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>\n              <path d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>\n              <path d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>\n            </svg>\n            <span>Sign in with Google/YouTube</span>\n          </button>\n\n          <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-4\">\n            We only access content you own or have purchased. Your authentication is secure and can be revoked at any time.\n          </p>\n        </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center\">\n            <svg className=\"w-5 h-5 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n            </svg>\n          </div>\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white\">\n              ✅ Authenticated as {session.user?.name}\n            </h3>\n            <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n              {session.user?.email}\n            </p>\n          </div>\n        </div>\n        <button\n          onClick={handleSignOut}\n          className=\"px-4 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n        >\n          Sign Out\n        </button>\n      </div>\n\n      {loading && (\n        <div className=\"flex items-center space-x-3 text-sm text-gray-600 dark:text-gray-300 mb-4\">\n          <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"></div>\n          <span>Loading your YouTube content...</span>\n        </div>\n      )}\n\n      {error && (\n        <div className=\"mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\">\n          <p className=\"text-red-800 dark:text-red-200 text-sm\">{error}</p>\n          <button\n            onClick={fetchUserContent}\n            className=\"mt-2 text-sm text-red-600 dark:text-red-400 hover:underline\"\n          >\n            Try again\n          </button>\n        </div>\n      )}\n\n      {userContent && (\n        <div className=\"space-y-4\">\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n            <div className=\"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-center\">\n              <div className=\"text-2xl font-bold text-blue-600 dark:text-blue-400\">\n                {userContent.totalPurchased}\n              </div>\n              <div className=\"text-sm text-blue-800 dark:text-blue-200\">\n                Purchased Movies\n              </div>\n            </div>\n            <div className=\"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg text-center\">\n              <div className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n                {userContent.totalPrivateVideos}\n              </div>\n              <div className=\"text-sm text-green-800 dark:text-green-200\">\n                Private Videos\n              </div>\n            </div>\n            <div className=\"bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg text-center\">\n              <div className=\"text-2xl font-bold text-purple-600 dark:text-purple-400\">\n                {userContent.totalPlaylists}\n              </div>\n              <div className=\"text-sm text-purple-800 dark:text-purple-200\">\n                Playlists\n              </div>\n            </div>\n            <div className=\"bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg text-center\">\n              <div className=\"text-2xl font-bold text-orange-600 dark:text-orange-400\">\n                {userContent.channel?.videoCount || 0}\n              </div>\n              <div className=\"text-sm text-orange-800 dark:text-orange-200\">\n                Your Videos\n              </div>\n            </div>\n          </div>\n\n          {userContent.channel && (\n            <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-gray-800 dark:text-white mb-2\">\n                📺 Your Channel: {userContent.channel.title}\n              </h4>\n              <div className=\"text-xs text-gray-600 dark:text-gray-300 space-y-1\">\n                <div>Subscribers: {parseInt(userContent.channel.subscriberCount || 0).toLocaleString()}</div>\n                <div>Total Views: {parseInt(userContent.channel.viewCount || 0).toLocaleString()}</div>\n              </div>\n            </div>\n          )}\n\n          <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\">\n            <h4 className=\"text-sm font-medium text-green-800 dark:text-green-200 mb-2\">\n              🎯 Ready for Authenticated Downloads\n            </h4>\n            <p className=\"text-xs text-green-700 dark:text-green-300\">\n              You can now download your purchased content, private videos, and playlists with full quality and metadata.\n            </p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUe,SAAS,YAAY,EAAE,YAAY,EAAoB;IACpE,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,aAAa;YACxB,eAAe,MAAM,QAAQ,IAAI;YACjC;QACF,OAAO;YACL,eAAe;YACf,eAAe;QACjB;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,mBAAmB;QACvB,IAAI,CAAC,SAAS,aAAa;QAE3B,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,IAAI,KAAK,cAAc,EAAE;oBACvB,SAAS;oBACT;gBACF;gBACA,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;YAClC;YAEA,eAAe,KAAK,IAAI;QAC1B,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,UAAU;YACf,aAAa,OAAO,QAAQ,CAAC,IAAI;QACnC;IACF;IAEA,oEAAoE;IACpE,MAAM,gBAAgB,oDAAyB;IAC/C,MAAM,gBAAgB,MAAM,QAAQ,CAAC,YAAY,MAAM,QAAQ,CAAC;IAEhE,MAAM,gBAAgB;QACpB,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;YACN,aAAa,OAAO,QAAQ,CAAC,IAAI;QACnC;QACA,eAAe;IACjB;IAEA,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAK,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAI3D;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;;gBAEZ,+BAAiB,8OAAC,gIAAA,CAAA,UAAU;;;;;8BAE7B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAyC,MAAK;4CAAe,SAAQ;sDAClF,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;kDAGZ,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAGzE,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;0CAKvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;;0DACnD,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;;;;;;;kDAEV,8OAAC;kDAAK;;;;;;;;;;;;0CAGR,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;;;;;;;;;;;;;;;;;;IAOrE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAA6C,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACpG,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;4CAAsD;4CAC9C,QAAQ,IAAI,EAAE;;;;;;;kDAEpC,8OAAC;wCAAE,WAAU;kDACV,QAAQ,IAAI,EAAE;;;;;;;;;;;;;;;;;;kCAIrB,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAKF,yBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;kCAAK;;;;;;;;;;;;YAIT,uBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAA0C;;;;;;kCACvD,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAMJ,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,YAAY,cAAc;;;;;;kDAE7B,8OAAC;wCAAI,WAAU;kDAA2C;;;;;;;;;;;;0CAI5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,YAAY,kBAAkB;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;kDAA6C;;;;;;;;;;;;0CAI9D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,YAAY,cAAc;;;;;;kDAE7B,8OAAC;wCAAI,WAAU;kDAA+C;;;;;;;;;;;;0CAIhE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,YAAY,OAAO,EAAE,cAAc;;;;;;kDAEtC,8OAAC;wCAAI,WAAU;kDAA+C;;;;;;;;;;;;;;;;;;oBAMjE,YAAY,OAAO,kBAClB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAyD;oCACnD,YAAY,OAAO,CAAC,KAAK;;;;;;;0CAE7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAI;4CAAc,SAAS,YAAY,OAAO,CAAC,eAAe,IAAI,GAAG,cAAc;;;;;;;kDACpF,8OAAC;;4CAAI;4CAAc,SAAS,YAAY,OAAO,CAAC,SAAS,IAAI,GAAG,cAAc;;;;;;;;;;;;;;;;;;;kCAKpF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAG5E,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;;;;;;;;;;;;;;;;;;;AAQtE", "debugId": null}}, {"offset": {"line": 6844, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/PurchasedContentBrowser.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport Image from 'next/image';\n\ninterface PurchasedContent {\n  id: string;\n  title: string;\n  type: 'movie' | 'episode' | 'season' | 'video';\n  thumbnail: string;\n  duration?: string;\n  publishedAt?: string;\n  channelTitle?: string;\n  isPrivate?: boolean;\n  isPurchased?: boolean;\n}\n\ninterface PurchasedContentBrowserProps {\n  onDownload: (content: PurchasedContent) => void;\n}\n\nexport default function PurchasedContentBrowser({ onDownload }: PurchasedContentBrowserProps) {\n  const { data: session } = useSession();\n  const [content, setContent] = useState<{\n    purchasedMovies: PurchasedContent[];\n    privateVideos: PurchasedContent[];\n    playlists: any[];\n  }>({\n    purchasedMovies: [],\n    privateVideos: [],\n    playlists: []\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [activeTab, setActiveTab] = useState<'purchased' | 'private' | 'playlists'>('purchased');\n  const [selectedPlaylist, setSelectedPlaylist] = useState<string | null>(null);\n  const [playlistVideos, setPlaylistVideos] = useState<PurchasedContent[]>([]);\n\n  useEffect(() => {\n    if (session?.accessToken) {\n      fetchContent();\n    }\n  }, [session]);\n\n  const fetchContent = async () => {\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await fetch('/api/youtube/purchased');\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.message || 'Failed to fetch content');\n      }\n\n      setContent(data.data);\n    } catch (err: any) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchPlaylistVideos = async (playlistId: string) => {\n    try {\n      const response = await fetch(`/api/youtube/playlist/${playlistId}`);\n      const data = await response.json();\n\n      if (response.ok) {\n        setPlaylistVideos(data.data || []);\n        setSelectedPlaylist(playlistId);\n      }\n    } catch (err) {\n      console.error('Error fetching playlist videos:', err);\n    }\n  };\n\n  const handleDownload = async (item: PurchasedContent) => {\n    try {\n      // Verify ownership before download\n      const response = await fetch('/api/youtube/purchased', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          videoId: item.id,\n          action: 'checkOwnership'\n        })\n      });\n\n      const data = await response.json();\n\n      if (data.success && data.data.isOwned) {\n        onDownload(item);\n      } else {\n        alert('You do not have permission to download this content.');\n      }\n    } catch (err) {\n      console.error('Error verifying ownership:', err);\n      alert('Failed to verify content ownership.');\n    }\n  };\n\n  const formatDuration = (duration: string) => {\n    if (!duration) return '';\n    // Convert ISO 8601 duration to readable format\n    const match = duration.match(/PT(\\d+H)?(\\d+M)?(\\d+S)?/);\n    if (!match) return duration;\n    \n    const hours = match[1] ? parseInt(match[1]) : 0;\n    const minutes = match[2] ? parseInt(match[2]) : 0;\n    const seconds = match[3] ? parseInt(match[3]) : 0;\n    \n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  };\n\n  if (!session) {\n    return (\n      <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-8 text-center\">\n        <p className=\"text-gray-600 dark:text-gray-300\">\n          Please sign in to browse your purchased content\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white\">\n          📚 Your YouTube Content Library\n        </h3>\n        <button\n          onClick={fetchContent}\n          disabled={loading}\n          className=\"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 transition-colors\"\n        >\n          {loading ? 'Refreshing...' : 'Refresh'}\n        </button>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"flex space-x-1 mb-6 bg-gray-100 dark:bg-gray-700 rounded-lg p-1\">\n        {[\n          { key: 'purchased', label: '🎬 Purchased', count: content.purchasedMovies.length },\n          { key: 'private', label: '🔒 Private Videos', count: content.privateVideos.length },\n          { key: 'playlists', label: '📋 Playlists', count: content.playlists.length }\n        ].map((tab) => (\n          <button\n            key={tab.key}\n            onClick={() => {\n              setActiveTab(tab.key as any);\n              setSelectedPlaylist(null);\n            }}\n            className={`flex-1 px-3 py-2 text-sm rounded-md transition-colors ${\n              activeTab === tab.key\n                ? 'bg-white dark:bg-gray-600 text-gray-800 dark:text-white shadow'\n                : 'text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white'\n            }`}\n          >\n            {tab.label} ({tab.count})\n          </button>\n        ))}\n      </div>\n\n      {loading && (\n        <div className=\"flex items-center justify-center py-8\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n          <span className=\"ml-3 text-gray-600 dark:text-gray-300\">Loading your content...</span>\n        </div>\n      )}\n\n      {error && (\n        <div className=\"mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\">\n          <p className=\"text-red-800 dark:text-red-200\">{error}</p>\n        </div>\n      )}\n\n      {!loading && !error && (\n        <div className=\"space-y-4\">\n          {/* Purchased Movies */}\n          {activeTab === 'purchased' && (\n            <div>\n              {content.purchasedMovies.length === 0 ? (\n                <div className=\"text-center py-8 text-gray-500 dark:text-gray-400\">\n                  <p>No purchased movies found</p>\n                  <p className=\"text-sm mt-1\">Purchased content will appear here</p>\n                </div>\n              ) : (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                  {content.purchasedMovies.map((movie) => (\n                    <div key={movie.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                      <div className=\"flex items-center space-x-3 mb-3\">\n                        <div className=\"w-16 h-12 bg-gray-200 dark:bg-gray-600 rounded overflow-hidden\">\n                          {movie.thumbnail && (\n                            <Image\n                              src={movie.thumbnail}\n                              alt={movie.title}\n                              width={64}\n                              height={48}\n                              className=\"w-full h-full object-cover\"\n                              unoptimized\n                            />\n                          )}\n                        </div>\n                        <div className=\"flex-1 min-w-0\">\n                          <h4 className=\"text-sm font-medium text-gray-800 dark:text-white truncate\">\n                            {movie.title}\n                          </h4>\n                          <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                            {movie.type} • {formatDuration(movie.duration || '')}\n                          </p>\n                        </div>\n                      </div>\n                      <button\n                        onClick={() => handleDownload(movie)}\n                        className=\"w-full px-3 py-2 text-sm bg-green-600 text-white rounded hover:bg-green-700 transition-colors\"\n                      >\n                        Download\n                      </button>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* Private Videos */}\n          {activeTab === 'private' && (\n            <div>\n              {content.privateVideos.length === 0 ? (\n                <div className=\"text-center py-8 text-gray-500 dark:text-gray-400\">\n                  <p>No private videos found</p>\n                  <p className=\"text-sm mt-1\">Your private videos will appear here</p>\n                </div>\n              ) : (\n                <div className=\"space-y-3\">\n                  {content.privateVideos.map((video) => (\n                    <div key={video.id} className=\"flex items-center space-x-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                      <div className=\"w-20 h-15 bg-gray-200 dark:bg-gray-600 rounded overflow-hidden\">\n                        {video.thumbnail && (\n                          <Image\n                            src={video.thumbnail}\n                            alt={video.title}\n                            width={80}\n                            height={60}\n                            className=\"w-full h-full object-cover\"\n                            unoptimized\n                          />\n                        )}\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <h4 className=\"text-sm font-medium text-gray-800 dark:text-white truncate\">\n                          {video.title}\n                        </h4>\n                        <div className=\"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400\">\n                          <span>{formatDuration(video.duration || '')}</span>\n                          <span>•</span>\n                          <span>{video.channelTitle}</span>\n                          {video.isPrivate && (\n                            <>\n                              <span>•</span>\n                              <span className=\"text-red-600 dark:text-red-400\">🔒 Private</span>\n                            </>\n                          )}\n                        </div>\n                      </div>\n                      <button\n                        onClick={() => handleDownload(video)}\n                        className=\"px-4 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\"\n                      >\n                        Download\n                      </button>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* Playlists */}\n          {activeTab === 'playlists' && (\n            <div>\n              {!selectedPlaylist ? (\n                <div>\n                  {content.playlists.length === 0 ? (\n                    <div className=\"text-center py-8 text-gray-500 dark:text-gray-400\">\n                      <p>No playlists found</p>\n                      <p className=\"text-sm mt-1\">Your playlists will appear here</p>\n                    </div>\n                  ) : (\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      {content.playlists.map((playlist) => (\n                        <div key={playlist.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                          <div className=\"flex items-center space-x-3 mb-3\">\n                            <div className=\"w-16 h-12 bg-gray-200 dark:bg-gray-600 rounded overflow-hidden\">\n                              {playlist.thumbnail && (\n                                <Image\n                                  src={playlist.thumbnail}\n                                  alt={playlist.title}\n                                  width={64}\n                                  height={48}\n                                  className=\"w-full h-full object-cover\"\n                                  unoptimized\n                                />\n                              )}\n                            </div>\n                            <div className=\"flex-1 min-w-0\">\n                              <h4 className=\"text-sm font-medium text-gray-800 dark:text-white truncate\">\n                                {playlist.title}\n                              </h4>\n                              <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                                {playlist.itemCount} videos\n                              </p>\n                            </div>\n                          </div>\n                          <button\n                            onClick={() => fetchPlaylistVideos(playlist.id)}\n                            className=\"w-full px-3 py-2 text-sm bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors\"\n                          >\n                            Browse Videos\n                          </button>\n                        </div>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              ) : (\n                <div>\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h4 className=\"text-lg font-medium text-gray-800 dark:text-white\">\n                      Playlist Videos\n                    </h4>\n                    <button\n                      onClick={() => setSelectedPlaylist(null)}\n                      className=\"text-sm text-blue-600 dark:text-blue-400 hover:underline\"\n                    >\n                      ← Back to Playlists\n                    </button>\n                  </div>\n                  <div className=\"space-y-3\">\n                    {playlistVideos.map((video) => (\n                      <div key={video.id} className=\"flex items-center space-x-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                        <div className=\"w-20 h-15 bg-gray-200 dark:bg-gray-600 rounded overflow-hidden\">\n                          {video.thumbnail && (\n                            <Image\n                              src={video.thumbnail}\n                              alt={video.title}\n                              width={80}\n                              height={60}\n                              className=\"w-full h-full object-cover\"\n                              unoptimized\n                            />\n                          )}\n                        </div>\n                        <div className=\"flex-1 min-w-0\">\n                          <h4 className=\"text-sm font-medium text-gray-800 dark:text-white truncate\">\n                            {video.title}\n                          </h4>\n                          <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                            {video.channelTitle}\n                          </p>\n                        </div>\n                        <button\n                          onClick={() => handleDownload(video)}\n                          className=\"px-4 py-2 text-sm bg-green-600 text-white rounded hover:bg-green-700 transition-colors\"\n                        >\n                          Download\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAsBe,SAAS,wBAAwB,EAAE,UAAU,EAAgC;IAC1F,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAIlC;QACD,iBAAiB,EAAE;QACnB,eAAe,EAAE;QACjB,WAAW,EAAE;IACf;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyC;IAClF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IAE3E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,aAAa;YACxB;QACF;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,eAAe;QACnB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;YAClC;YAEA,WAAW,KAAK,IAAI;QACtB,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,YAAY;YAClE,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,kBAAkB,KAAK,IAAI,IAAI,EAAE;gBACjC,oBAAoB;YACtB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,mCAAmC;YACnC,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,KAAK,EAAE;oBAChB,QAAQ;gBACV;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,CAAC,OAAO,EAAE;gBACrC,WAAW;YACb,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,UAAU,OAAO;QACtB,+CAA+C;QAC/C,MAAM,QAAQ,SAAS,KAAK,CAAC;QAC7B,IAAI,CAAC,OAAO,OAAO;QAEnB,MAAM,QAAQ,KAAK,CAAC,EAAE,GAAG,SAAS,KAAK,CAAC,EAAE,IAAI;QAC9C,MAAM,UAAU,KAAK,CAAC,EAAE,GAAG,SAAS,KAAK,CAAC,EAAE,IAAI;QAChD,MAAM,UAAU,KAAK,CAAC,EAAE,GAAG,SAAS,KAAK,CAAC,EAAE,IAAI;QAEhD,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;QACjG;QACA,OAAO,GAAG,QAAQ,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC5D;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAE,WAAU;0BAAmC;;;;;;;;;;;IAKtD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsD;;;;;;kCAGpE,8OAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,UAAU,kBAAkB;;;;;;;;;;;;0BAKjC,8OAAC;gBAAI,WAAU;0BACZ;oBACC;wBAAE,KAAK;wBAAa,OAAO;wBAAgB,OAAO,QAAQ,eAAe,CAAC,MAAM;oBAAC;oBACjF;wBAAE,KAAK;wBAAW,OAAO;wBAAqB,OAAO,QAAQ,aAAa,CAAC,MAAM;oBAAC;oBAClF;wBAAE,KAAK;wBAAa,OAAO;wBAAgB,OAAO,QAAQ,SAAS,CAAC,MAAM;oBAAC;iBAC5E,CAAC,GAAG,CAAC,CAAC,oBACL,8OAAC;wBAEC,SAAS;4BACP,aAAa,IAAI,GAAG;4BACpB,oBAAoB;wBACtB;wBACA,WAAW,CAAC,sDAAsD,EAChE,cAAc,IAAI,GAAG,GACjB,mEACA,8EACJ;;4BAED,IAAI,KAAK;4BAAC;4BAAG,IAAI,KAAK;4BAAC;;uBAXnB,IAAI,GAAG;;;;;;;;;;YAgBjB,yBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAK,WAAU;kCAAwC;;;;;;;;;;;;YAI3D,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAkC;;;;;;;;;;;YAIlD,CAAC,WAAW,CAAC,uBACZ,8OAAC;gBAAI,WAAU;;oBAEZ,cAAc,6BACb,8OAAC;kCACE,QAAQ,eAAe,CAAC,MAAM,KAAK,kBAClC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAE,WAAU;8CAAe;;;;;;;;;;;iDAG9B,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,eAAe,CAAC,GAAG,CAAC,CAAC,sBAC5B,8OAAC;oCAAmB,WAAU;;sDAC5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,MAAM,SAAS,kBACd,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,MAAM,SAAS;wDACpB,KAAK,MAAM,KAAK;wDAChB,OAAO;wDACP,QAAQ;wDACR,WAAU;wDACV,WAAW;;;;;;;;;;;8DAIjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,MAAM,KAAK;;;;;;sEAEd,8OAAC;4DAAE,WAAU;;gEACV,MAAM,IAAI;gEAAC;gEAAI,eAAe,MAAM,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;sDAIvD,8OAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDACX;;;;;;;mCA1BO,MAAM,EAAE;;;;;;;;;;;;;;;oBAqC3B,cAAc,2BACb,8OAAC;kCACE,QAAQ,aAAa,CAAC,MAAM,KAAK,kBAChC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAE,WAAU;8CAAe;;;;;;;;;;;iDAG9B,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,aAAa,CAAC,GAAG,CAAC,CAAC,sBAC1B,8OAAC;oCAAmB,WAAU;;sDAC5B,8OAAC;4CAAI,WAAU;sDACZ,MAAM,SAAS,kBACd,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,MAAM,SAAS;gDACpB,KAAK,MAAM,KAAK;gDAChB,OAAO;gDACP,QAAQ;gDACR,WAAU;gDACV,WAAW;;;;;;;;;;;sDAIjB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,MAAM,KAAK;;;;;;8DAEd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAM,eAAe,MAAM,QAAQ,IAAI;;;;;;sEACxC,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAM,MAAM,YAAY;;;;;;wDACxB,MAAM,SAAS,kBACd;;8EACE,8OAAC;8EAAK;;;;;;8EACN,8OAAC;oEAAK,WAAU;8EAAiC;;;;;;;;;;;;;;;;;;;;sDAKzD,8OAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDACX;;;;;;;mCAhCO,MAAM,EAAE;;;;;;;;;;;;;;;oBA2C3B,cAAc,6BACb,8OAAC;kCACE,CAAC,iCACA,8OAAC;sCACE,QAAQ,SAAS,CAAC,MAAM,KAAK,kBAC5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAE;;;;;;kDACH,8OAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;qDAG9B,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAC,yBACtB,8OAAC;wCAAsB,WAAU;;0DAC/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,SAAS,SAAS,kBACjB,8OAAC,6HAAA,CAAA,UAAK;4DACJ,KAAK,SAAS,SAAS;4DACvB,KAAK,SAAS,KAAK;4DACnB,OAAO;4DACP,QAAQ;4DACR,WAAU;4DACV,WAAW;;;;;;;;;;;kEAIjB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EACX,SAAS,KAAK;;;;;;0EAEjB,8OAAC;gEAAE,WAAU;;oEACV,SAAS,SAAS;oEAAC;;;;;;;;;;;;;;;;;;;0DAI1B,8OAAC;gDACC,SAAS,IAAM,oBAAoB,SAAS,EAAE;gDAC9C,WAAU;0DACX;;;;;;;uCA1BO,SAAS,EAAE;;;;;;;;;;;;;;iDAmC7B,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAGlE,8OAAC;4CACC,SAAS,IAAM,oBAAoB;4CACnC,WAAU;sDACX;;;;;;;;;;;;8CAIH,8OAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC;4CAAmB,WAAU;;8DAC5B,8OAAC;oDAAI,WAAU;8DACZ,MAAM,SAAS,kBACd,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,MAAM,SAAS;wDACpB,KAAK,MAAM,KAAK;wDAChB,OAAO;wDACP,QAAQ;wDACR,WAAU;wDACV,WAAW;;;;;;;;;;;8DAIjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,MAAM,KAAK;;;;;;sEAEd,8OAAC;4DAAE,WAAU;sEACV,MAAM,YAAY;;;;;;;;;;;;8DAGvB,8OAAC;oDACC,SAAS,IAAM,eAAe;oDAC9B,WAAU;8DACX;;;;;;;2CAxBO,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCxC", "debugId": null}}, {"offset": {"line": 7556, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/components/VideoDownloader.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport VideoInfoCard from './VideoInfoCard';\nimport DownloadProgress from './DownloadProgress';\nimport DownloadHistory from './DownloadHistory';\nimport BatchDownloader from './BatchDownloader';\nimport SettingsPanel from './SettingsPanel';\nimport DownloadQueue from './DownloadQueue';\nimport SiteDirectory from './SiteDirectory';\nimport URLValidator from './URLValidator';\nimport PlatformSpecificOptions from './PlatformSpecificOptions';\nimport YouTubeAuth from './YouTubeAuth';\nimport PurchasedContentBrowser from './PurchasedContentBrowser';\nimport { SiteInfo } from '@/data/supportedSites';\n\ninterface VideoInfo {\n  title: string;\n  description: string;\n  duration: number;\n  uploader: string;\n  upload_date: string;\n  view_count: number;\n  thumbnail: string;\n  formats: Array<{\n    format_id: string;\n    ext: string;\n    quality: string;\n    filesize: number;\n    format_note: string;\n    vcodec: string;\n    acodec: string;\n    fps: number;\n    resolution: string;\n  }>;\n  url: string;\n  webpage_url: string;\n  extractor: string;\n}\n\ninterface DownloadStatus {\n  id: string;\n  status: 'downloading' | 'completed' | 'failed';\n  progress: number;\n  filename: string | null;\n  url: string;\n  startTime: number;\n  error: string | null;\n}\n\nexport default function VideoDownloader() {\n  const [url, setUrl] = useState('');\n  const [videoInfo, setVideoInfo] = useState<VideoInfo | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [activeDownloads, setActiveDownloads] = useState<DownloadStatus[]>([]);\n  const [downloadHistory, setDownloadHistory] = useState<any[]>([]);\n  const [activeBatchDownloads, setActiveBatchDownloads] = useState<any[]>([]);\n  const [showSettings, setShowSettings] = useState(false);\n  const [downloadQueue, setDownloadQueue] = useState<any[]>([]);\n  const [detectedSite, setDetectedSite] = useState<SiteInfo | null>(null);\n  const [platformOptions, setPlatformOptions] = useState<any>({});\n  const [isYouTubeAuthenticated, setIsYouTubeAuthenticated] = useState(false);\n  const [youTubeUserData, setYouTubeUserData] = useState<any>(null);\n  const [settings, setSettings] = useState<any>({\n    maxConcurrentDownloads: 3,\n    defaultQuality: 'best',\n    defaultAudioFormat: 'mp3',\n    autoSubtitles: false,\n    defaultSubtitleLanguages: 'en'\n  });\n\n  const fetchVideoInfo = async () => {\n    if (!url.trim()) {\n      setError('Please enter a valid URL');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    setVideoInfo(null);\n\n    try {\n      const response = await fetch('/api/video-info', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ url: url.trim() }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to fetch video information');\n      }\n\n      if (data.success) {\n        setVideoInfo(data.data);\n      } else {\n        throw new Error(data.error || 'Unknown error occurred');\n      }\n    } catch (err: any) {\n      setError(err.message || 'Failed to fetch video information');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const startDownload = async (format?: string, quality?: string, type?: string, audioFormat?: string, subtitleOptions?: any) => {\n    if (!videoInfo) return;\n\n    try {\n      const response = await fetch('/api/download', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          url: videoInfo.url,\n          format,\n          quality,\n          type,\n          audioFormat,\n          subtitleOptions,\n          platformOptions,\n          siteInfo: detectedSite,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to start download');\n      }\n\n      if (data.success) {\n        // Add to active downloads\n        const newDownload: DownloadStatus = {\n          id: data.downloadId,\n          status: 'downloading',\n          progress: 0,\n          filename: null,\n          url: videoInfo.url,\n          startTime: Date.now(),\n          error: null,\n        };\n\n        setActiveDownloads(prev => [...prev, newDownload]);\n        \n        // Start polling for progress\n        pollDownloadProgress(data.downloadId);\n      }\n    } catch (err: any) {\n      setError(err.message || 'Failed to start download');\n    }\n  };\n\n  const pollDownloadProgress = async (downloadId: string) => {\n    const poll = async () => {\n      try {\n        const response = await fetch(`/api/download?id=${downloadId}`);\n        const data = await response.json();\n\n        if (data.success) {\n          setActiveDownloads(prev =>\n            prev.map(download =>\n              download.id === downloadId\n                ? { ...download, ...data.data }\n                : download\n            )\n          );\n\n          // If download is completed or failed, stop polling\n          if (data.data.status === 'completed' || data.data.status === 'failed') {\n            if (data.data.status === 'completed') {\n              // Refresh download history\n              fetchDownloadHistory();\n            }\n            return;\n          }\n\n          // Continue polling\n          setTimeout(poll, 2000);\n        }\n      } catch (err) {\n        console.error('Error polling download progress:', err);\n      }\n    };\n\n    poll();\n  };\n\n  const fetchDownloadHistory = async () => {\n    try {\n      const response = await fetch('/api/downloads');\n      const data = await response.json();\n\n      if (data.success) {\n        setDownloadHistory(data.data);\n      }\n    } catch (err) {\n      console.error('Error fetching download history:', err);\n    }\n  };\n\n  const deleteDownload = async (filename: string) => {\n    try {\n      const response = await fetch('/api/downloads', {\n        method: 'DELETE',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ filename }),\n      });\n\n      if (response.ok) {\n        fetchDownloadHistory();\n      }\n    } catch (err) {\n      console.error('Error deleting download:', err);\n    }\n  };\n\n  const handleBatchDownload = async (urls: string[], options: any) => {\n    try {\n      const response = await fetch('/api/batch-download', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ urls, options }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to start batch download');\n      }\n\n      if (data.success) {\n        // Add to active batch downloads\n        const newBatchDownload = {\n          id: data.batchId,\n          status: 'downloading',\n          progress: 0,\n          urls: urls,\n          startTime: Date.now(),\n          totalItems: data.totalUrls,\n          completedItems: 0,\n          currentItem: '',\n          files: []\n        };\n\n        setActiveBatchDownloads(prev => [...prev, newBatchDownload]);\n\n        // Start polling for progress\n        pollBatchDownloadProgress(data.batchId);\n      }\n    } catch (err: any) {\n      setError(err.message || 'Failed to start batch download');\n    }\n  };\n\n  const pollBatchDownloadProgress = async (batchId: string) => {\n    const poll = async () => {\n      try {\n        const response = await fetch(`/api/batch-download?id=${batchId}`);\n        const data = await response.json();\n\n        if (data.success) {\n          setActiveBatchDownloads(prev =>\n            prev.map(batch =>\n              batch.id === batchId\n                ? { ...batch, ...data.data }\n                : batch\n            )\n          );\n\n          // If batch is completed or failed, stop polling\n          if (data.data.status === 'completed' || data.data.status === 'failed') {\n            if (data.data.status === 'completed') {\n              // Refresh download history\n              fetchDownloadHistory();\n            }\n            return;\n          }\n\n          // Continue polling\n          setTimeout(poll, 3000);\n        }\n      } catch (err) {\n        console.error('Error polling batch download progress:', err);\n      }\n    };\n\n    poll();\n  };\n\n  // Fetch download history on component mount\n  useEffect(() => {\n    fetchDownloadHistory();\n  }, []);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    fetchVideoInfo();\n  };\n\n  // Queue Management Functions\n  const handlePauseDownload = (id: string) => {\n    console.log('Pausing download:', id);\n  };\n\n  const handleResumeDownload = (id: string) => {\n    console.log('Resuming download:', id);\n  };\n\n  const handleCancelDownload = (id: string) => {\n    setDownloadQueue(prev => prev.filter(item => item.id !== id));\n    setActiveDownloads(prev => prev.filter(item => item.id !== id));\n  };\n\n  const handleRetryDownload = (id: string) => {\n    console.log('Retrying download:', id);\n  };\n\n  const handlePriorityChange = (id: string, priority: 'low' | 'normal' | 'high') => {\n    setDownloadQueue(prev =>\n      prev.map(item =>\n        item.id === id ? { ...item, priority } : item\n      )\n    );\n  };\n\n  const handleQueueReorder = (fromIndex: number, toIndex: number) => {\n    setDownloadQueue(prev => {\n      const newQueue = [...prev];\n      const [removed] = newQueue.splice(fromIndex, 1);\n      newQueue.splice(toIndex, 0, removed);\n      return newQueue;\n    });\n  };\n\n  const handleSettingsChange = (newSettings: any) => {\n    setSettings(newSettings);\n  };\n\n  const handleSiteSelect = (site: SiteInfo) => {\n    // When a site is selected from the directory, we could pre-fill some options\n    setDetectedSite(site);\n  };\n\n  const handleExampleSelect = (exampleUrl: string) => {\n    setUrl(exampleUrl);\n  };\n\n  const handleURLValidation = (isValid: boolean, siteInfo: SiteInfo | null) => {\n    setDetectedSite(siteInfo);\n  };\n\n  const handlePlatformOptionsChange = (options: any) => {\n    setPlatformOptions(options);\n  };\n\n  const handleYouTubeAuthChange = (isAuthenticated: boolean, userData?: any) => {\n    setIsYouTubeAuthenticated(isAuthenticated);\n    setYouTubeUserData(userData);\n  };\n\n  const handlePurchasedContentDownload = async (content: any) => {\n    try {\n      const youtubeUrl = `https://www.youtube.com/watch?v=${content.id}`;\n\n      const response = await fetch('/api/youtube/download', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          url: youtubeUrl,\n          format: 'best',\n          quality: '',\n          type: 'video',\n          verifyOwnership: true,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.message || 'Failed to start authenticated download');\n      }\n\n      if (data.success) {\n        // Add to active downloads with special authenticated flag\n        const newDownload = {\n          id: data.downloadId,\n          status: 'downloading',\n          progress: 0,\n          filename: null,\n          url: youtubeUrl,\n          startTime: Date.now(),\n          error: null,\n          isAuthenticated: true,\n          videoDetails: data.videoDetails,\n        };\n\n        setActiveDownloads(prev => [...prev, newDownload]);\n\n        // Start polling for progress\n        pollAuthenticatedDownloadProgress(data.downloadId);\n      }\n    } catch (err: any) {\n      setError(err.message || 'Failed to start authenticated download');\n    }\n  };\n\n  const pollAuthenticatedDownloadProgress = async (downloadId: string) => {\n    const poll = async () => {\n      try {\n        const response = await fetch(`/api/youtube/download?id=${downloadId}`);\n        const data = await response.json();\n\n        if (data.success) {\n          setActiveDownloads(prev =>\n            prev.map(download =>\n              download.id === downloadId\n                ? { ...download, ...data.data }\n                : download\n            )\n          );\n\n          // If download is completed or failed, stop polling\n          if (data.data.status === 'completed' || data.data.status === 'failed') {\n            if (data.data.status === 'completed') {\n              // Refresh download history\n              fetchDownloadHistory();\n            }\n            return;\n          }\n\n          // Continue polling\n          setTimeout(poll, 2000);\n        }\n      } catch (err) {\n        console.error('Error polling authenticated download progress:', err);\n      }\n    };\n\n    poll();\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-8\">\n      {/* Header with Settings */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-800 dark:text-white\">\n            Universal Video Downloader\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-300\">\n            Enhanced with advanced features and queue management\n          </p>\n        </div>\n        <button\n          onClick={() => setShowSettings(true)}\n          className=\"px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n        >\n          ⚙️ Settings\n        </button>\n      </div>\n\n      {/* YouTube Authentication */}\n      <YouTubeAuth onAuthChange={handleYouTubeAuthChange} />\n\n      {/* Purchased Content Browser */}\n      {isYouTubeAuthenticated && (\n        <PurchasedContentBrowser onDownload={handlePurchasedContentDownload} />\n      )}\n\n      {/* Site Directory */}\n      <SiteDirectory\n        onSiteSelect={handleSiteSelect}\n        onExampleSelect={handleExampleSelect}\n      />\n\n      {/* Download Queue */}\n      <DownloadQueue\n        queue={downloadQueue}\n        onPause={handlePauseDownload}\n        onResume={handleResumeDownload}\n        onCancel={handleCancelDownload}\n        onRetry={handleRetryDownload}\n        onPriorityChange={handlePriorityChange}\n        onReorder={handleQueueReorder}\n        maxConcurrent={settings.maxConcurrentDownloads}\n      />\n\n      {/* Batch Downloader */}\n      <BatchDownloader onBatchDownload={handleBatchDownload} />\n\n      {/* URL Input Form */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div>\n            <label htmlFor=\"url\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Video URL\n            </label>\n            <div className=\"flex gap-2\">\n              <input\n                type=\"url\"\n                id=\"url\"\n                value={url}\n                onChange={(e) => setUrl(e.target.value)}\n                placeholder=\"Enter video URL (YouTube, Vimeo, etc.)\"\n                className=\"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n                disabled={loading}\n              />\n              <button\n                type=\"submit\"\n                disabled={loading || !url.trim()}\n                className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                {loading ? 'Analyzing...' : 'Analyze'}\n              </button>\n            </div>\n          </div>\n        </form>\n\n        {/* URL Validator */}\n        <URLValidator\n          url={url}\n          onValidationChange={handleURLValidation}\n        />\n\n        {/* Platform Specific Options */}\n        {detectedSite && (\n          <PlatformSpecificOptions\n            siteInfo={detectedSite}\n            onOptionsChange={handlePlatformOptionsChange}\n          />\n        )}\n\n        {error && (\n          <div className=\"mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\">\n            <p className=\"text-red-800 dark:text-red-200\">{error}</p>\n          </div>\n        )}\n      </div>\n\n      {/* Video Information */}\n      {videoInfo && (\n        <VideoInfoCard videoInfo={videoInfo} onDownload={startDownload} />\n      )}\n\n      {/* Active Downloads */}\n      {activeDownloads.length > 0 && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white mb-4\">\n            Active Downloads\n          </h3>\n          <div className=\"space-y-4\">\n            {activeDownloads.map((download) => (\n              <DownloadProgress key={download.id} download={download} />\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Active Batch Downloads */}\n      {activeBatchDownloads.length > 0 && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white mb-4\">\n            📦 Active Batch Downloads\n          </h3>\n          <div className=\"space-y-4\">\n            {activeBatchDownloads.map((batch) => (\n              <div key={batch.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <div className=\"flex-1 min-w-0\">\n                    <p className=\"text-sm font-medium text-gray-800 dark:text-white\">\n                      Batch Download ({batch.completedItems}/{batch.totalItems} items)\n                    </p>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      {batch.currentItem || 'Preparing...'}\n                    </p>\n                  </div>\n                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white ${\n                    batch.status === 'downloading' ? 'bg-blue-600' :\n                    batch.status === 'completed' ? 'bg-green-600' : 'bg-red-600'\n                  }`}>\n                    {batch.status === 'downloading' ? 'Downloading...' :\n                     batch.status === 'completed' ? 'Completed' : 'Failed'}\n                  </span>\n                </div>\n\n                <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2\">\n                  <div\n                    className={`h-2 rounded-full transition-all duration-300 ${\n                      batch.status === 'downloading' ? 'bg-blue-600' :\n                      batch.status === 'completed' ? 'bg-green-600' : 'bg-red-600'\n                    }`}\n                    style={{ width: `${Math.max(0, Math.min(100, batch.progress))}%` }}\n                  ></div>\n                </div>\n\n                <div className=\"flex justify-between items-center text-xs text-gray-500 dark:text-gray-400\">\n                  <span>{batch.progress.toFixed(1)}%</span>\n                  <span>{batch.urls.length} URL(s)</span>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Download History */}\n      <DownloadHistory\n        downloads={downloadHistory}\n        onDelete={deleteDownload}\n        onRefresh={fetchDownloadHistory}\n      />\n\n      {/* Settings Panel */}\n      <SettingsPanel\n        isOpen={showSettings}\n        onClose={() => setShowSettings(false)}\n        onSettingsChange={handleSettingsChange}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;;AAkDe,SAAS;IACtB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC3E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAChE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAClE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IAC7D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;QAC5C,wBAAwB;QACxB,gBAAgB;QAChB,oBAAoB;QACpB,eAAe;QACf,0BAA0B;IAC5B;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,IAAI,IAAI,IAAI;YACf,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QACT,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,KAAK,IAAI,IAAI;gBAAG;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,IAAI,KAAK,OAAO,EAAE;gBAChB,aAAa,KAAK,IAAI;YACxB,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO,QAAiB,SAAkB,MAAe,aAAsB;QACnG,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,KAAK,UAAU,GAAG;oBAClB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA,UAAU;gBACZ;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,IAAI,KAAK,OAAO,EAAE;gBAChB,0BAA0B;gBAC1B,MAAM,cAA8B;oBAClC,IAAI,KAAK,UAAU;oBACnB,QAAQ;oBACR,UAAU;oBACV,UAAU;oBACV,KAAK,UAAU,GAAG;oBAClB,WAAW,KAAK,GAAG;oBACnB,OAAO;gBACT;gBAEA,mBAAmB,CAAA,OAAQ;2BAAI;wBAAM;qBAAY;gBAEjD,6BAA6B;gBAC7B,qBAAqB,KAAK,UAAU;YACtC;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,MAAM,OAAO;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,YAAY;gBAC7D,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,mBAAmB,CAAA,OACjB,KAAK,GAAG,CAAC,CAAA,WACP,SAAS,EAAE,KAAK,aACZ;gCAAE,GAAG,QAAQ;gCAAE,GAAG,KAAK,IAAI;4BAAC,IAC5B;oBAIR,mDAAmD;oBACnD,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,eAAe,KAAK,IAAI,CAAC,MAAM,KAAK,UAAU;wBACrE,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,aAAa;4BACpC,2BAA2B;4BAC3B;wBACF;wBACA;oBACF;oBAEA,mBAAmB;oBACnB,WAAW,MAAM;gBACnB;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;QAEA;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,mBAAmB,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,kBAAkB;gBAC7C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,sBAAsB,OAAO,MAAgB;QACjD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM;gBAAQ;YACvC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,IAAI,KAAK,OAAO,EAAE;gBAChB,gCAAgC;gBAChC,MAAM,mBAAmB;oBACvB,IAAI,KAAK,OAAO;oBAChB,QAAQ;oBACR,UAAU;oBACV,MAAM;oBACN,WAAW,KAAK,GAAG;oBACnB,YAAY,KAAK,SAAS;oBAC1B,gBAAgB;oBAChB,aAAa;oBACb,OAAO,EAAE;gBACX;gBAEA,wBAAwB,CAAA,OAAQ;2BAAI;wBAAM;qBAAiB;gBAE3D,6BAA6B;gBAC7B,0BAA0B,KAAK,OAAO;YACxC;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B;IACF;IAEA,MAAM,4BAA4B,OAAO;QACvC,MAAM,OAAO;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,SAAS;gBAChE,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,wBAAwB,CAAA,OACtB,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,EAAE,KAAK,UACT;gCAAE,GAAG,KAAK;gCAAE,GAAG,KAAK,IAAI;4BAAC,IACzB;oBAIR,gDAAgD;oBAChD,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,eAAe,KAAK,IAAI,CAAC,MAAM,KAAK,UAAU;wBACrE,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,aAAa;4BACpC,2BAA2B;4BAC3B;wBACF;wBACA;oBACF;oBAEA,mBAAmB;oBACnB,WAAW,MAAM;gBACnB;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,0CAA0C;YAC1D;QACF;QAEA;IACF;IAEA,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB;IACF;IAEA,6BAA6B;IAC7B,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,GAAG,CAAC,qBAAqB;IACnC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,QAAQ,GAAG,CAAC,sBAAsB;IACpC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACzD,mBAAmB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAC7D;IAEA,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,GAAG,CAAC,sBAAsB;IACpC;IAEA,MAAM,uBAAuB,CAAC,IAAY;QACxC,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,KAAK;oBAAE,GAAG,IAAI;oBAAE;gBAAS,IAAI;IAG/C;IAEA,MAAM,qBAAqB,CAAC,WAAmB;QAC7C,iBAAiB,CAAA;YACf,MAAM,WAAW;mBAAI;aAAK;YAC1B,MAAM,CAAC,QAAQ,GAAG,SAAS,MAAM,CAAC,WAAW;YAC7C,SAAS,MAAM,CAAC,SAAS,GAAG;YAC5B,OAAO;QACT;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY;IACd;IAEA,MAAM,mBAAmB,CAAC;QACxB,6EAA6E;QAC7E,gBAAgB;IAClB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAO;IACT;IAEA,MAAM,sBAAsB,CAAC,SAAkB;QAC7C,gBAAgB;IAClB;IAEA,MAAM,8BAA8B,CAAC;QACnC,mBAAmB;IACrB;IAEA,MAAM,0BAA0B,CAAC,iBAA0B;QACzD,0BAA0B;QAC1B,mBAAmB;IACrB;IAEA,MAAM,iCAAiC,OAAO;QAC5C,IAAI;YACF,MAAM,aAAa,CAAC,gCAAgC,EAAE,QAAQ,EAAE,EAAE;YAElE,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,KAAK;oBACL,QAAQ;oBACR,SAAS;oBACT,MAAM;oBACN,iBAAiB;gBACnB;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;YAClC;YAEA,IAAI,KAAK,OAAO,EAAE;gBAChB,0DAA0D;gBAC1D,MAAM,cAAc;oBAClB,IAAI,KAAK,UAAU;oBACnB,QAAQ;oBACR,UAAU;oBACV,UAAU;oBACV,KAAK;oBACL,WAAW,KAAK,GAAG;oBACnB,OAAO;oBACP,iBAAiB;oBACjB,cAAc,KAAK,YAAY;gBACjC;gBAEA,mBAAmB,CAAA,OAAQ;2BAAI;wBAAM;qBAAY;gBAEjD,6BAA6B;gBAC7B,kCAAkC,KAAK,UAAU;YACnD;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B;IACF;IAEA,MAAM,oCAAoC,OAAO;QAC/C,MAAM,OAAO;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,yBAAyB,EAAE,YAAY;gBACrE,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,mBAAmB,CAAA,OACjB,KAAK,GAAG,CAAC,CAAA,WACP,SAAS,EAAE,KAAK,aACZ;gCAAE,GAAG,QAAQ;gCAAE,GAAG,KAAK,IAAI;4BAAC,IAC5B;oBAIR,mDAAmD;oBACnD,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,eAAe,KAAK,IAAI,CAAC,MAAM,KAAK,UAAU;wBACrE,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,aAAa;4BACpC,2BAA2B;4BAC3B;wBACF;wBACA;oBACF;oBAEA,mBAAmB;oBACnB,WAAW,MAAM;gBACnB;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,kDAAkD;YAClE;QACF;QAEA;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;kCAIlD,8OAAC;wBACC,SAAS,IAAM,gBAAgB;wBAC/B,WAAU;kCACX;;;;;;;;;;;;0BAMH,8OAAC,iIAAA,CAAA,UAAW;gBAAC,cAAc;;;;;;YAG1B,wCACC,8OAAC,6IAAA,CAAA,UAAuB;gBAAC,YAAY;;;;;;0BAIvC,8OAAC,mIAAA,CAAA,UAAa;gBACZ,cAAc;gBACd,iBAAiB;;;;;;0BAInB,8OAAC,mIAAA,CAAA,UAAa;gBACZ,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,UAAU;gBACV,SAAS;gBACT,kBAAkB;gBAClB,WAAW;gBACX,eAAe,SAAS,sBAAsB;;;;;;0BAIhD,8OAAC,qIAAA,CAAA,UAAe;gBAAC,iBAAiB;;;;;;0BAGlC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,UAAU;wBAAc,WAAU;kCACtC,cAAA,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAM,WAAU;8CAAkE;;;;;;8CAGjG,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK;4CACtC,aAAY;4CACZ,WAAU;4CACV,UAAU;;;;;;sDAEZ,8OAAC;4CACC,MAAK;4CACL,UAAU,WAAW,CAAC,IAAI,IAAI;4CAC9B,WAAU;sDAET,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;kCAOpC,8OAAC,kIAAA,CAAA,UAAY;wBACX,KAAK;wBACL,oBAAoB;;;;;;oBAIrB,8BACC,8OAAC,6IAAA,CAAA,UAAuB;wBACtB,UAAU;wBACV,iBAAiB;;;;;;oBAIpB,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAkC;;;;;;;;;;;;;;;;;YAMpD,2BACC,8OAAC,mIAAA,CAAA,UAAa;gBAAC,WAAW;gBAAW,YAAY;;;;;;YAIlD,gBAAgB,MAAM,GAAG,mBACxB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,yBACpB,8OAAC,sIAAA,CAAA,UAAgB;gCAAmB,UAAU;+BAAvB,SAAS,EAAE;;;;;;;;;;;;;;;;YAOzC,qBAAqB,MAAM,GAAG,mBAC7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,8OAAC;wBAAI,WAAU;kCACZ,qBAAqB,GAAG,CAAC,CAAC,sBACzB,8OAAC;gCAAmB,WAAU;;kDAC5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;;4DAAoD;4DAC9C,MAAM,cAAc;4DAAC;4DAAE,MAAM,UAAU;4DAAC;;;;;;;kEAE3D,8OAAC;wDAAE,WAAU;kEACV,MAAM,WAAW,IAAI;;;;;;;;;;;;0DAG1B,8OAAC;gDAAK,WAAW,CAAC,mFAAmF,EACnG,MAAM,MAAM,KAAK,gBAAgB,gBACjC,MAAM,MAAM,KAAK,cAAc,iBAAiB,cAChD;0DACC,MAAM,MAAM,KAAK,gBAAgB,mBACjC,MAAM,MAAM,KAAK,cAAc,cAAc;;;;;;;;;;;;kDAIlD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAW,CAAC,6CAA6C,EACvD,MAAM,MAAM,KAAK,gBAAgB,gBACjC,MAAM,MAAM,KAAK,cAAc,iBAAiB,cAChD;4CACF,OAAO;gDAAE,OAAO,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,MAAM,QAAQ,GAAG,CAAC,CAAC;4CAAC;;;;;;;;;;;kDAIrE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAM,MAAM,QAAQ,CAAC,OAAO,CAAC;oDAAG;;;;;;;0DACjC,8OAAC;;oDAAM,MAAM,IAAI,CAAC,MAAM;oDAAC;;;;;;;;;;;;;;+BA/BnB,MAAM,EAAE;;;;;;;;;;;;;;;;0BAwC1B,8OAAC,qIAAA,CAAA,UAAe;gBACd,WAAW;gBACX,UAAU;gBACV,WAAW;;;;;;0BAIb,8OAAC,mIAAA,CAAA,UAAa;gBACZ,QAAQ;gBACR,SAAS,IAAM,gBAAgB;gBAC/B,kBAAkB;;;;;;;;;;;;AAI1B", "debugId": null}}, {"offset": {"line": 8319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport VideoDownloader from '@/components/VideoDownloader';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <header className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-800 dark:text-white mb-2\">\n            Universal Video Downloader\n          </h1>\n          <p className=\"text-lg text-gray-600 dark:text-gray-300\">\n            Download videos from any website with ease\n          </p>\n          <div className=\"mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\">\n            <p className=\"text-sm text-yellow-800 dark:text-yellow-200\">\n              <strong>Legal Notice:</strong> Please respect copyright laws and only download videos you have permission to download.\n              This tool is intended for personal use, educational purposes, and downloading content you own or have explicit permission to download.\n            </p>\n          </div>\n        </header>\n\n        <main>\n          <VideoDownloader />\n        </main>\n\n        <footer className=\"mt-16 text-center text-gray-500 dark:text-gray-400\">\n          <p className=\"text-sm\">\n            Powered by yt-dlp • Built with Next.js and Tailwind CSS\n          </p>\n        </footer>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,8OAAC;4BAAE,WAAU;sCAA2C;;;;;;sCAGxD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;kDAAO;;;;;;oCAAsB;;;;;;;;;;;;;;;;;;8BAMpC,8OAAC;8BACC,cAAA,8OAAC,qIAAA,CAAA,UAAe;;;;;;;;;;8BAGlB,8OAAC;oBAAO,WAAU;8BAChB,cAAA,8OAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;;;;;;;;;;;;AAOjC", "debugId": null}}]}