'use client';

import { useState, useEffect } from 'react';
import { findSiteByDomain, SiteInfo } from '@/data/supportedSites';

interface URLValidatorProps {
  url: string;
  onValidationChange?: (isValid: boolean, siteInfo: SiteInfo | null) => void;
}

export default function URLValidator({ url, onValidationChange }: URLValidatorProps) {
  const [siteInfo, setSiteInfo] = useState<SiteInfo | null>(null);
  const [isValid, setIsValid] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [validationMessage, setValidationMessage] = useState('');

  useEffect(() => {
    if (!url.trim()) {
      setSiteInfo(null);
      setIsValid(false);
      setValidationMessage('');
      onValidationChange?.(false, null);
      return;
    }

    validateURL(url);
  }, [url]);

  const validateURL = async (inputUrl: string) => {
    setIsChecking(true);
    
    try {
      // Basic URL validation
      const urlObj = new URL(inputUrl);
      
      // Find site info
      const detectedSite = findSiteByDomain(inputUrl);
      
      if (detectedSite) {
        setSiteInfo(detectedSite);
        setIsValid(true);
        setValidationMessage(`✅ Supported ${detectedSite.category} platform`);
        onValidationChange?.(true, detectedSite);
      } else {
        // Check if it might still be supported (yt-dlp supports many unlisted sites)
        setSiteInfo(null);
        setIsValid(true); // Assume it might work
        setValidationMessage('⚠️ Site not in our directory, but may still be supported');
        onValidationChange?.(true, null);
      }
    } catch (error) {
      setSiteInfo(null);
      setIsValid(false);
      setValidationMessage('❌ Invalid URL format');
      onValidationChange?.(false, null);
    } finally {
      setIsChecking(false);
    }
  };

  if (!url.trim()) {
    return null;
  }

  return (
    <div className="mt-2">
      {isChecking ? (
        <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          <span>Checking URL...</span>
        </div>
      ) : (
        <div className="space-y-2">
          {/* Validation Status */}
          <div className={`text-sm ${
            isValid 
              ? siteInfo 
                ? 'text-green-600 dark:text-green-400' 
                : 'text-yellow-600 dark:text-yellow-400'
              : 'text-red-600 dark:text-red-400'
          }`}>
            {validationMessage}
          </div>

          {/* Site Information Card */}
          {siteInfo && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
              <div className="flex items-center space-x-3 mb-2">
                <span className="text-lg">{siteInfo.icon}</span>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                    {siteInfo.name}
                  </h4>
                  <p className="text-xs text-blue-600 dark:text-blue-300">
                    {siteInfo.description}
                  </p>
                </div>
                <span className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded">
                  {siteInfo.category}
                </span>
              </div>

              {/* Capabilities */}
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex items-center space-x-1">
                  <span>📺</span>
                  <span className="text-blue-700 dark:text-blue-300">
                    Max Quality: {siteInfo.maxQuality}
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <span>🎵</span>
                  <span className="text-blue-700 dark:text-blue-300">
                    Audio: {siteInfo.audioFormats.join(', ')}
                  </span>
                </div>
                {siteInfo.supportsPlaylists && (
                  <div className="flex items-center space-x-1">
                    <span>📋</span>
                    <span className="text-blue-700 dark:text-blue-300">Playlists</span>
                  </div>
                )}
                {siteInfo.supportsLive && (
                  <div className="flex items-center space-x-1">
                    <span>🔴</span>
                    <span className="text-blue-700 dark:text-blue-300">Live Streams</span>
                  </div>
                )}
                {siteInfo.supportsSubtitles && (
                  <div className="flex items-center space-x-1">
                    <span>📝</span>
                    <span className="text-blue-700 dark:text-blue-300">Subtitles</span>
                  </div>
                )}
              </div>

              {/* Features */}
              <div className="mt-2">
                <div className="flex flex-wrap gap-1">
                  {siteInfo.features.slice(0, 4).map((feature) => (
                    <span
                      key={feature}
                      className="px-2 py-0.5 text-xs bg-blue-100 dark:bg-blue-800 text-blue-700 dark:text-blue-200 rounded"
                    >
                      {feature}
                    </span>
                  ))}
                  {siteInfo.features.length > 4 && (
                    <span className="px-2 py-0.5 text-xs bg-blue-100 dark:bg-blue-800 text-blue-700 dark:text-blue-200 rounded">
                      +{siteInfo.features.length - 4} more
                    </span>
                  )}
                </div>
              </div>

              {/* Notes/Warnings */}
              {siteInfo.notes && (
                <div className="mt-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded">
                  <p className="text-xs text-yellow-800 dark:text-yellow-200">
                    ⚠️ {siteInfo.notes}
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Unknown Site Info */}
          {!siteInfo && isValid && (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-lg">🌐</span>
                <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                  Unknown Site
                </h4>
              </div>
              <p className="text-xs text-yellow-700 dark:text-yellow-300 mb-2">
                This site is not in our featured directory, but yt-dlp supports over 1000+ sites. 
                It may still work perfectly!
              </p>
              <div className="text-xs text-yellow-600 dark:text-yellow-400">
                💡 Try the "Analyze" button to check if this site is supported
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
