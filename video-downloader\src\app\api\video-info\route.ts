import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json();

    if (!url) {
      return NextResponse.json({ error: 'URL is required' }, { status: 400 });
    }

    // Validate URL format
    try {
      new URL(url);
    } catch {
      return NextResponse.json({ error: 'Invalid URL format' }, { status: 400 });
    }

    // Use yt-dlp to extract video information
    const command = `python -m yt_dlp --dump-json --no-download "${url}"`;
    
    try {
      const { stdout, stderr } = await execAsync(command, { 
        timeout: 30000, // 30 second timeout
        maxBuffer: 1024 * 1024 * 10 // 10MB buffer
      });

      if (stderr && !stdout) {
        console.error('yt-dlp stderr:', stderr);
        return NextResponse.json({ 
          error: 'Failed to extract video information',
          details: stderr 
        }, { status: 500 });
      }

      // Parse the JSON output from yt-dlp
      const videoInfo = JSON.parse(stdout);
      
      // Extract relevant information
      const extractedInfo = {
        title: videoInfo.title || 'Unknown Title',
        description: videoInfo.description || '',
        duration: videoInfo.duration || 0,
        uploader: videoInfo.uploader || 'Unknown',
        upload_date: videoInfo.upload_date || '',
        view_count: videoInfo.view_count || 0,
        thumbnail: videoInfo.thumbnail || '',
        formats: videoInfo.formats?.map((format: any) => ({
          format_id: format.format_id,
          ext: format.ext,
          quality: format.quality || format.height || 'unknown',
          filesize: format.filesize,
          format_note: format.format_note || '',
          vcodec: format.vcodec,
          acodec: format.acodec,
          fps: format.fps,
          resolution: format.resolution || `${format.width}x${format.height}` || 'unknown'
        })) || [],
        url: url,
        webpage_url: videoInfo.webpage_url || url,
        extractor: videoInfo.extractor || 'unknown'
      };

      return NextResponse.json({ 
        success: true, 
        data: extractedInfo 
      });

    } catch (execError: any) {
      console.error('yt-dlp execution error:', execError);
      
      if (execError.code === 'ETIMEDOUT') {
        return NextResponse.json({ 
          error: 'Request timeout - the video source may be slow to respond' 
        }, { status: 408 });
      }

      return NextResponse.json({ 
        error: 'Failed to extract video information',
        details: execError.message 
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('API error:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error.message 
    }, { status: 500 });
  }
}
