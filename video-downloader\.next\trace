[{"name": "hot-reloader", "duration": 146, "timestamp": 177049384460, "id": 3, "tags": {"version": "15.3.5"}, "startTime": 1752338563915, "traceId": "e055d86fda6e8fb1"}, {"name": "setup-dev-bundler", "duration": 1664221, "timestamp": 177048880266, "id": 2, "parentId": 1, "tags": {}, "startTime": 1752338563411, "traceId": "e055d86fda6e8fb1"}, {"name": "run-instrumentation-hook", "duration": 49, "timestamp": 177050738900, "id": 4, "parentId": 1, "tags": {}, "startTime": 1752338565269, "traceId": "e055d86fda6e8fb1"}, {"name": "start-dev-server", "duration": 3799288, "timestamp": 177047001224, "id": 1, "tags": {"cpus": "8", "platform": "win32", "memory.freeMem": "1199677440", "memory.totalMem": "6378106880", "memory.heapSizeLimit": "3390046208", "memory.rss": "194871296", "memory.heapTotal": "137969664", "memory.heapUsed": "65836512"}, "startTime": 1752338561532, "traceId": "e055d86fda6e8fb1"}, {"name": "compile-path", "duration": 7825829, "timestamp": 177065080142, "id": 7, "tags": {"trigger": "/"}, "startTime": 1752338579610, "traceId": "e055d86fda6e8fb1"}, {"name": "ensure-page", "duration": 7829769, "timestamp": 177065077178, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1752338579608, "traceId": "e055d86fda6e8fb1"}]