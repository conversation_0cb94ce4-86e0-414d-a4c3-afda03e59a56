'use client';

import { useState } from 'react';
import Image from 'next/image';

interface VideoInfo {
  title: string;
  description: string;
  duration: number;
  uploader: string;
  upload_date: string;
  view_count: number;
  thumbnail: string;
  formats: Array<{
    format_id: string;
    ext: string;
    quality: string;
    filesize: number;
    format_note: string;
    vcodec: string;
    acodec: string;
    fps: number;
    resolution: string;
  }>;
  url: string;
  webpage_url: string;
  extractor: string;
}

interface VideoInfoCardProps {
  videoInfo: VideoInfo;
  onDownload: (format?: string, quality?: string, type?: string, audioFormat?: string, subtitleOptions?: any) => void;
}

export default function VideoInfoCard({ videoInfo, onDownload }: VideoInfoCardProps) {
  const [selectedFormat, setSelectedFormat] = useState('best');
  const [selectedQuality, setSelectedQuality] = useState('');
  const [downloadType, setDownloadType] = useState<'video' | 'audio'>('video');
  const [audioFormat, setAudioFormat] = useState('mp3');
  const [includeSubtitles, setIncludeSubtitles] = useState(false);
  const [subtitleLanguages, setSubtitleLanguages] = useState('en');

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Unknown';
    const year = dateString.substring(0, 4);
    const month = dateString.substring(4, 6);
    const day = dateString.substring(6, 8);
    return `${year}-${month}-${day}`;
  };

  const formatViewCount = (count: number) => {
    if (count >= 1000000) {
      return (count / 1000000).toFixed(1) + 'M';
    } else if (count >= 1000) {
      return (count / 1000).toFixed(1) + 'K';
    }
    return count.toString();
  };

  const handleDownload = () => {
    const subtitleOptions = includeSubtitles ? {
      includeSubtitles: true,
      subtitleLanguages: subtitleLanguages
    } : undefined;

    if (downloadType === 'audio') {
      onDownload(`bestaudio[ext=${audioFormat}]/bestaudio`, undefined, 'audio', audioFormat, subtitleOptions);
    } else if (selectedFormat === 'best') {
      onDownload(undefined, undefined, undefined, undefined, subtitleOptions);
    } else if (selectedFormat === 'quality' && selectedQuality) {
      onDownload(undefined, selectedQuality, undefined, undefined, subtitleOptions);
    } else {
      onDownload(selectedFormat, undefined, undefined, undefined, subtitleOptions);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
      <div className="md:flex">
        <div className="md:w-1/3">
          {videoInfo.thumbnail && (
            <div className="relative aspect-video">
              <Image
                src={videoInfo.thumbnail}
                alt={videoInfo.title}
                fill
                className="object-cover"
                unoptimized
              />
            </div>
          )}
        </div>

        <div className="md:w-2/3 p-6">
          <h2 className="text-xl font-bold text-gray-800 dark:text-white mb-2">
            {videoInfo.title}
          </h2>
          
          <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-300 mb-4">
            <div>
              <span className="font-medium">Uploader:</span> {videoInfo.uploader}
            </div>
            <div>
              <span className="font-medium">Duration:</span> {formatDuration(videoInfo.duration)}
            </div>
            <div>
              <span className="font-medium">Views:</span> {formatViewCount(videoInfo.view_count)}
            </div>
            <div>
              <span className="font-medium">Upload Date:</span> {formatDate(videoInfo.upload_date)}
            </div>
            <div>
              <span className="font-medium">Source:</span> {videoInfo.extractor}
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Download Type
              </label>
              <div className="flex space-x-4 mb-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="downloadType"
                    value="video"
                    checked={downloadType === 'video'}
                    onChange={(e) => setDownloadType(e.target.value as 'video' | 'audio')}
                    className="mr-2"
                  />
                  <span className="text-sm">🎥 Video</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="downloadType"
                    value="audio"
                    checked={downloadType === 'audio'}
                    onChange={(e) => setDownloadType(e.target.value as 'video' | 'audio')}
                    className="mr-2"
                  />
                  <span className="text-sm">🎵 Audio Only</span>
                </label>
              </div>
            </div>

            {downloadType === 'audio' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Audio Format
                </label>
                <select
                  value={audioFormat}
                  onChange={(e) => setAudioFormat(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
                >
                  <option value="mp3">MP3 (Most Compatible)</option>
                  <option value="m4a">M4A (High Quality)</option>
                  <option value="wav">WAV (Uncompressed)</option>
                  <option value="flac">FLAC (Lossless)</option>
                  <option value="ogg">OGG (Open Source)</option>
                </select>
              </div>
            )}

            <div>
              <label className="flex items-center mb-2">
                <input
                  type="checkbox"
                  checked={includeSubtitles}
                  onChange={(e) => setIncludeSubtitles(e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  📝 Download Subtitles/Captions
                </span>
              </label>
              
              {includeSubtitles && (
                <div className="ml-6">
                  <input
                    type="text"
                    value={subtitleLanguages}
                    onChange={(e) => setSubtitleLanguages(e.target.value)}
                    placeholder="en,es,fr"
                    className="w-full px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Language codes (comma-separated): en, es, fr, de, ja
                  </p>
                </div>
              )}
            </div>

            {downloadType === 'video' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Video Quality Options
                </label>
                
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="format"
                      value="best"
                      checked={selectedFormat === 'best'}
                      onChange={(e) => setSelectedFormat(e.target.value)}
                      className="mr-2"
                    />
                    <span className="text-sm">Best Quality (Automatic)</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="format"
                      value="quality"
                      checked={selectedFormat === 'quality'}
                      onChange={(e) => setSelectedFormat(e.target.value)}
                      className="mr-2"
                    />
                    <span className="text-sm">Select Quality:</span>
                    <select
                      value={selectedQuality}
                      onChange={(e) => {
                        setSelectedQuality(e.target.value);
                        setSelectedFormat('quality');
                      }}
                      className="ml-2 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm dark:bg-gray-700 dark:text-white"
                      disabled={selectedFormat !== 'quality'}
                    >
                      <option value="">Select...</option>
                      <option value="720">720p</option>
                      <option value="480">480p</option>
                      <option value="360">360p</option>
                      <option value="240">240p</option>
                    </select>
                  </label>
                </div>
              </div>
            )}

            <button
              onClick={handleDownload}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors"
            >
              {downloadType === 'audio' ? `Extract Audio (${audioFormat.toUpperCase()})` : 'Download Video'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
