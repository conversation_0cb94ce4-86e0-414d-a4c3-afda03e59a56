'use client';

import { useState } from 'react';
import Image from 'next/image';

interface VideoInfo {
  title: string;
  description: string;
  duration: number;
  uploader: string;
  upload_date: string;
  view_count: number;
  thumbnail: string;
  formats: Array<{
    format_id: string;
    ext: string;
    quality: string;
    filesize: number;
    format_note: string;
    vcodec: string;
    acodec: string;
    fps: number;
    resolution: string;
  }>;
  url: string;
  webpage_url: string;
  extractor: string;
}

interface VideoInfoCardProps {
  videoInfo: VideoInfo;
  onDownload: (format?: string, quality?: string) => void;
}

export default function VideoInfoCard({ videoInfo, onDownload }: VideoInfoCardProps) {
  const [selectedFormat, setSelectedFormat] = useState('best');
  const [selectedQuality, setSelectedQuality] = useState('');

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes: number) => {
    if (!bytes) return 'Unknown';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Unknown';
    const year = dateString.substring(0, 4);
    const month = dateString.substring(4, 6);
    const day = dateString.substring(6, 8);
    return `${year}-${month}-${day}`;
  };

  const formatViewCount = (count: number) => {
    if (count >= 1000000) {
      return (count / 1000000).toFixed(1) + 'M';
    } else if (count >= 1000) {
      return (count / 1000).toFixed(1) + 'K';
    }
    return count.toString();
  };

  // Group formats by quality for better display
  const videoFormats = videoInfo.formats
    .filter(f => f.vcodec && f.vcodec !== 'none')
    .sort((a, b) => {
      const aHeight = parseInt(a.resolution?.split('x')[1] || '0');
      const bHeight = parseInt(b.resolution?.split('x')[1] || '0');
      return bHeight - aHeight;
    });

  const audioFormats = videoInfo.formats
    .filter(f => f.acodec && f.acodec !== 'none' && (!f.vcodec || f.vcodec === 'none'))
    .sort((a, b) => (b.filesize || 0) - (a.filesize || 0));

  const handleDownload = () => {
    if (selectedFormat === 'best') {
      onDownload();
    } else if (selectedFormat === 'quality' && selectedQuality) {
      onDownload(undefined, selectedQuality);
    } else {
      onDownload(selectedFormat);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
      <div className="md:flex">
        {/* Thumbnail */}
        <div className="md:w-1/3">
          {videoInfo.thumbnail && (
            <div className="relative aspect-video">
              <Image
                src={videoInfo.thumbnail}
                alt={videoInfo.title}
                fill
                className="object-cover"
                unoptimized
              />
            </div>
          )}
        </div>

        {/* Video Info */}
        <div className="md:w-2/3 p-6">
          <h2 className="text-xl font-bold text-gray-800 dark:text-white mb-2">
            {videoInfo.title}
          </h2>
          
          <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-300 mb-4">
            <div>
              <span className="font-medium">Uploader:</span> {videoInfo.uploader}
            </div>
            <div>
              <span className="font-medium">Duration:</span> {formatDuration(videoInfo.duration)}
            </div>
            <div>
              <span className="font-medium">Views:</span> {formatViewCount(videoInfo.view_count)}
            </div>
            <div>
              <span className="font-medium">Upload Date:</span> {formatDate(videoInfo.upload_date)}
            </div>
            <div>
              <span className="font-medium">Source:</span> {videoInfo.extractor}
            </div>
          </div>

          {videoInfo.description && (
            <div className="mb-4">
              <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-3">
                {videoInfo.description.substring(0, 200)}
                {videoInfo.description.length > 200 && '...'}
              </p>
            </div>
          )}

          {/* Download Options */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Download Options
              </label>
              
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="format"
                    value="best"
                    checked={selectedFormat === 'best'}
                    onChange={(e) => setSelectedFormat(e.target.value)}
                    className="mr-2"
                  />
                  <span className="text-sm">Best Quality (Automatic)</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="radio"
                    name="format"
                    value="quality"
                    checked={selectedFormat === 'quality'}
                    onChange={(e) => setSelectedFormat(e.target.value)}
                    className="mr-2"
                  />
                  <span className="text-sm">Select Quality:</span>
                  <select
                    value={selectedQuality}
                    onChange={(e) => {
                      setSelectedQuality(e.target.value);
                      setSelectedFormat('quality');
                    }}
                    className="ml-2 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm dark:bg-gray-700 dark:text-white"
                    disabled={selectedFormat !== 'quality'}
                  >
                    <option value="">Select...</option>
                    <option value="720">720p</option>
                    <option value="480">480p</option>
                    <option value="360">360p</option>
                    <option value="240">240p</option>
                  </select>
                </label>

                {videoFormats.length > 0 && (
                  <details className="mt-2">
                    <summary className="cursor-pointer text-sm text-blue-600 dark:text-blue-400">
                      Advanced Format Selection
                    </summary>
                    <div className="mt-2 max-h-40 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded p-2">
                      {videoFormats.slice(0, 10).map((format) => (
                        <label key={format.format_id} className="flex items-center mb-1">
                          <input
                            type="radio"
                            name="format"
                            value={format.format_id}
                            checked={selectedFormat === format.format_id}
                            onChange={(e) => setSelectedFormat(e.target.value)}
                            className="mr-2"
                          />
                          <span className="text-xs">
                            {format.resolution} • {format.ext} • {formatFileSize(format.filesize)} • {format.format_note}
                          </span>
                        </label>
                      ))}
                    </div>
                  </details>
                )}
              </div>
            </div>

            <button
              onClick={handleDownload}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors"
            >
              Start Download
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
