'use client';

interface DownloadStatus {
  id: string;
  status: 'downloading' | 'completed' | 'failed';
  progress: number;
  filename: string | null;
  url: string;
  startTime: number;
  error: string | null;
}

interface DownloadProgressProps {
  download: DownloadStatus;
}

export default function DownloadProgress({ download }: DownloadProgressProps) {
  const getStatusColor = () => {
    switch (download.status) {
      case 'downloading':
        return 'bg-blue-600';
      case 'completed':
        return 'bg-green-600';
      case 'failed':
        return 'bg-red-600';
      default:
        return 'bg-gray-600';
    }
  };

  const getStatusText = () => {
    switch (download.status) {
      case 'downloading':
        return 'Downloading...';
      case 'completed':
        return 'Completed';
      case 'failed':
        return 'Failed';
      default:
        return 'Unknown';
    }
  };

  const formatElapsedTime = () => {
    const elapsed = Date.now() - download.startTime;
    const seconds = Math.floor(elapsed / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const getDisplayUrl = () => {
    try {
      const url = new URL(download.url);
      return url.hostname;
    } catch {
      return download.url.substring(0, 50) + '...';
    }
  };

  return (
    <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
      <div className="flex items-center justify-between mb-2">
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-800 dark:text-white truncate">
            {download.filename || 'Preparing download...'}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
            {getDisplayUrl()}
          </p>
        </div>
        <div className="ml-4 flex items-center space-x-2">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white ${getStatusColor()}`}>
            {getStatusText()}
          </span>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {formatElapsedTime()}
          </span>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2">
        <div
          className={`h-2 rounded-full transition-all duration-300 ${getStatusColor()}`}
          style={{ width: `${Math.max(0, Math.min(100, download.progress))}%` }}
        ></div>
      </div>

      <div className="flex justify-between items-center">
        <span className="text-xs text-gray-500 dark:text-gray-400">
          {download.progress.toFixed(1)}%
        </span>
        {download.status === 'completed' && download.filename && (
          <a
            href={`/downloads/${download.filename}`}
            download
            className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
          >
            Download File
          </a>
        )}
      </div>

      {download.error && (
        <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded">
          <p className="text-xs text-red-800 dark:text-red-200">
            Error: {download.error}
          </p>
        </div>
      )}
    </div>
  );
}
