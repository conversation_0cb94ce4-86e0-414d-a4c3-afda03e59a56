'use client';

import { signIn, signOut, useSession } from 'next-auth/react';

export default function TestAuth() {
  const { data: session, status } = useSession();

  if (status === 'loading') {
    return <div className="p-8">Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold mb-6">🔧 OAuth Test Page</h1>
        
        {!session ? (
          <div className="space-y-4">
            <p className="text-gray-600">Not signed in</p>
            <button
              onClick={() => signIn('google')}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Sign in with Google (Test)
            </button>
            
            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-800 mb-2">If you get an error:</h3>
              <ol className="text-sm text-yellow-700 space-y-1">
                <li>1. Check Google Cloud Console redirect URI is exactly: <code className="bg-yellow-100 px-1 rounded">http://localhost:3000/api/auth/callback/google</code></li>
                <li>2. Ensure OAuth consent screen is configured</li>
                <li>3. Make sure YouTube Data API v3 is enabled</li>
                <li>4. Check that your app is published or you're added as a test user</li>
              </ol>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="font-medium text-green-800">✅ Authentication Successful!</h3>
              <p className="text-sm text-green-700 mt-1">Signed in as: {session.user?.email}</p>
            </div>
            
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">Session Data:</h4>
              <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
                {JSON.stringify(session, null, 2)}
              </pre>
            </div>
            
            <button
              onClick={() => signOut()}
              className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700"
            >
              Sign out
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
