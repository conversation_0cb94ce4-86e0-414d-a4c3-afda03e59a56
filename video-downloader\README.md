# Universal Video Downloader

A modern web application that allows you to detect and download videos from various websites using yt-dlp. Built with Next.js, TypeScript, and Tailwind CSS.

## Features

- 🎥 **Universal Video Detection**: Supports hundreds of websites including YouTube, Vimeo, TikTok, and many more
- 📊 **Video Information Display**: Shows title, duration, uploader, views, and available formats
- ⚙️ **Quality Selection**: Choose from different video qualities and formats
- 📈 **Real-time Progress Tracking**: Monitor download progress with live updates
- 📁 **Download Management**: View download history and manage downloaded files
- 🎨 **Modern UI**: Clean, responsive interface with dark mode support
- ⚡ **Fast Performance**: Built with Next.js and optimized for speed

## Prerequisites

Before running this application, make sure you have:

- **Node.js** (version 18 or higher)
- **Python** (version 3.7 or higher)
- **yt-dlp** installed via pip

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd video-downloader
   ```

2. **Install Node.js dependencies**:
   ```bash
   npm install
   ```

3. **Install yt-dlp**:
   ```bash
   pip install yt-dlp
   ```

## Getting Started

1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **Open your browser** and navigate to [http://localhost:3000](http://localhost:3000)

3. **Enter a video URL** in the input field and click "Analyze" to detect video information

4. **Select your preferred quality/format** and click "Start Download"

5. **Monitor the download progress** in real-time

6. **Access downloaded files** from the download history section

## Supported Websites

This application supports video downloads from hundreds of websites, including:

- YouTube
- Vimeo
- TikTok
- Twitter/X
- Instagram
- Facebook
- Twitch
- And many more...

For a complete list, visit the [yt-dlp supported sites documentation](https://github.com/yt-dlp/yt-dlp/blob/master/supportedsites.md).

## API Endpoints

### POST /api/video-info
Extract video information from a URL.

**Request Body**:
```json
{
  "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
}
```

### POST /api/download
Start a video download.

**Request Body**:
```json
{
  "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
  "format": "best",
  "quality": "720"
}
```

### GET /api/download?id={downloadId}
Check download progress.

### GET /api/downloads
List all downloaded files.

### DELETE /api/downloads
Delete a downloaded file.

## Legal Notice

⚠️ **Important**: Please respect copyright laws and only download videos you have permission to download. This tool is intended for:

- Personal use and backup of your own content
- Educational purposes
- Downloading content you own or have explicit permission to download
- Fair use scenarios as defined by your local laws

The developers of this application are not responsible for any misuse of this tool.

## Technology Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Node.js
- **Video Processing**: yt-dlp (Python)
- **Styling**: Tailwind CSS with dark mode support

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is open source and available under the [MIT License](LICENSE).
