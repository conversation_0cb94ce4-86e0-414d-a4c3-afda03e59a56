{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/app/api/auth/debug/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function GET(request: NextRequest) {\n  // Only allow in development\n  if (process.env.NODE_ENV !== 'development') {\n    return NextResponse.json({ error: 'Not available in production' }, { status: 403 });\n  }\n\n  const diagnostics = {\n    environment: process.env.NODE_ENV,\n    nextAuthUrl: process.env.NEXTAUTH_URL,\n    hasClientId: !!process.env.GOOGLE_CLIENT_ID,\n    hasClientSecret: !!process.env.GOOGLE_CLIENT_SECRET,\n    hasNextAuthSecret: !!process.env.NEXTAUTH_SECRET,\n    clientIdFormat: process.env.GOOGLE_CLIENT_ID ? \n      (process.env.GOOGLE_CLIENT_ID.includes('.apps.googleusercontent.com') ? 'Valid format' : 'Invalid format') : \n      'Missing',\n    clientSecretFormat: process.env.GOOGLE_CLIENT_SECRET ? \n      (process.env.GOOGLE_CLIENT_SECRET.startsWith('GOCSPX-') ? 'Valid format' : 'Invalid format') : \n      'Missing',\n    expectedRedirectUri: 'http://localhost:3000/api/auth/callback/google',\n    troubleshooting: {\n      step1: 'Verify Client ID ends with .apps.googleusercontent.com',\n      step2: 'Verify Client Secret starts with GOCSPX-',\n      step3: 'Check redirect URI in Google Cloud Console matches exactly: http://localhost:3000/api/auth/callback/google',\n      step4: 'Ensure OAuth consent screen is configured',\n      step5: 'Make sure the Google project has YouTube Data API v3 enabled'\n    }\n  };\n\n  return NextResponse.json(diagnostics);\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,IAAI,OAAoB;IAC5C,4BAA4B;IAC5B,uCAA4C;;IAE5C;IAEA,MAAM,cAAc;QAClB,WAAW;QACX,aAAa,QAAQ,GAAG,CAAC,YAAY;QACrC,aAAa,CAAC,CAAC,QAAQ,GAAG,CAAC,gBAAgB;QAC3C,iBAAiB,CAAC,CAAC,QAAQ,GAAG,CAAC,oBAAoB;QACnD,mBAAmB,CAAC,CAAC,QAAQ,GAAG,CAAC,eAAe;QAChD,gBAAgB,QAAQ,GAAG,CAAC,gBAAgB,GACzC,QAAQ,GAAG,CAAC,gBAAgB,CAAC,QAAQ,CAAC,iCAAiC,iBAAiB,mBACzF;QACF,oBAAoB,QAAQ,GAAG,CAAC,oBAAoB,GACjD,QAAQ,GAAG,CAAC,oBAAoB,CAAC,UAAU,CAAC,aAAa,iBAAiB,mBAC3E;QACF,qBAAqB;QACrB,iBAAiB;YACf,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;QACT;IACF;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;AAC3B", "debugId": null}}]}