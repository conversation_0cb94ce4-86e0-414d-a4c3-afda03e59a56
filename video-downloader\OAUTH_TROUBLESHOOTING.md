# 🔧 OAuth Troubleshooting Guide

## Current Error: `invalid_client (Unauthorized)`

This error means Google is rejecting your OAuth request. Here's how to fix it:

## ✅ Step-by-Step Fix

### Step 1: Verify Google Cloud Console Setup

1. **Go to Google Cloud Console**: https://console.cloud.google.com/
2. **Select your project** (the one where you created the OAuth credentials)
3. **Navigate to**: APIs & Services → Credentials

### Step 2: Check OAuth 2.0 Client ID

1. **Find your OAuth client**: Look for `************-or0ld3218lc8uehlcl6qvfn6llpl0qn3`
2. **Click the edit icon** (pencil) next to it
3. **Verify the following**:

   **Application type**: Web application ✅
   
   **Authorized redirect URIs**: Must contain EXACTLY:
   ```
   http://localhost:3000/api/auth/callback/google
   ```
   
   **Important**: 
   - No trailing slash
   - Exact port (3000)
   - Must be `http://` not `https://` for localhost
   - Case sensitive

4. **Remove any other redirect URIs** if present
5. **Click Save**

### Step 3: Check OAuth Consent Screen

1. **Go to**: APIs & Services → OAuth consent screen
2. **Verify**:
   - App name is filled in
   - User support email is set
   - Developer contact information is provided
3. **Publishing status**:
   - Either set to "Published" 
   - OR add your email to "Test users" if in testing mode

### Step 4: Enable YouTube Data API

1. **Go to**: APIs & Services → Library
2. **Search for**: "YouTube Data API v3"
3. **Make sure it shows**: "API enabled" with a green checkmark
4. **If not enabled**: Click on it and click "Enable"

### Step 5: Verify Scopes

In OAuth consent screen, make sure these scopes are added:
- `../auth/userinfo.email`
- `../auth/userinfo.profile`
- `../auth/youtube.readonly`
- `../auth/youtube.force-ssl`

## 🔍 Common Issues & Solutions

### Issue 1: Wrong Redirect URI
**Problem**: Redirect URI doesn't match exactly
**Solution**: Must be exactly `http://localhost:3000/api/auth/callback/google`

### Issue 2: App Not Published
**Problem**: OAuth consent screen is in testing mode but you're not added as test user
**Solution**: Either publish the app OR add your email to test users

### Issue 3: Wrong Project
**Problem**: Using credentials from wrong Google Cloud project
**Solution**: Make sure you're in the correct project where YouTube API is enabled

### Issue 4: API Not Enabled
**Problem**: YouTube Data API v3 is not enabled
**Solution**: Enable it in APIs & Services → Library

## 🧪 Test After Each Fix

After making changes:
1. **Wait 1-2 minutes** for changes to propagate
2. **Restart your development server**:
   ```bash
   # Stop server (Ctrl+C)
   npm run dev
   ```
3. **Test at**: http://localhost:3000/test-auth
4. **Click**: "Sign in with Google (Test)"

## 📋 Checklist

- [ ] OAuth client type is "Web application"
- [ ] Redirect URI is exactly: `http://localhost:3000/api/auth/callback/google`
- [ ] No other redirect URIs are present
- [ ] OAuth consent screen is configured
- [ ] App is published OR you're added as test user
- [ ] YouTube Data API v3 is enabled
- [ ] Using correct Google Cloud project
- [ ] Waited 1-2 minutes after making changes
- [ ] Restarted development server

## 🆘 If Still Not Working

1. **Try creating new OAuth credentials**:
   - Delete current OAuth client
   - Create new one with exact same settings
   - Update .env.local with new credentials

2. **Check browser console** for additional error messages

3. **Verify project quotas** in Google Cloud Console

## 📞 Current Credentials

Your current setup:
- Client ID: `************-or0ld3218lc8uehlcl6qvfn6llpl0qn3.apps.googleusercontent.com`
- Client Secret: `GOCSPX-59wAVThkM-A7wx9dEhId0VYtS1`
- Expected Redirect: `http://localhost:3000/api/auth/callback/google`

The credentials format is correct, so the issue is in Google Cloud Console configuration.
