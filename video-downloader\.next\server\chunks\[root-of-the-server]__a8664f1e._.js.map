{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/disney%20plus%20downloader/video-downloader/src/app/api/video-info/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { exec } from 'child_process';\nimport { promisify } from 'util';\n\nconst execAsync = promisify(exec);\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { url } = await request.json();\n\n    if (!url) {\n      return NextResponse.json({ error: 'URL is required' }, { status: 400 });\n    }\n\n    // Validate URL format\n    try {\n      new URL(url);\n    } catch {\n      return NextResponse.json({ error: 'Invalid URL format' }, { status: 400 });\n    }\n\n    // Use yt-dlp to extract video information\n    const command = `python -m yt_dlp --dump-json --no-download \"${url}\"`;\n    \n    try {\n      const { stdout, stderr } = await execAsync(command, { \n        timeout: 30000, // 30 second timeout\n        maxBuffer: 1024 * 1024 * 10 // 10MB buffer\n      });\n\n      if (stderr && !stdout) {\n        console.error('yt-dlp stderr:', stderr);\n        return NextResponse.json({ \n          error: 'Failed to extract video information',\n          details: stderr \n        }, { status: 500 });\n      }\n\n      // Parse the JSON output from yt-dlp\n      const videoInfo = JSON.parse(stdout);\n      \n      // Extract relevant information\n      const extractedInfo = {\n        title: videoInfo.title || 'Unknown Title',\n        description: videoInfo.description || '',\n        duration: videoInfo.duration || 0,\n        uploader: videoInfo.uploader || 'Unknown',\n        upload_date: videoInfo.upload_date || '',\n        view_count: videoInfo.view_count || 0,\n        thumbnail: videoInfo.thumbnail || '',\n        formats: videoInfo.formats?.map((format: any) => ({\n          format_id: format.format_id,\n          ext: format.ext,\n          quality: format.quality || format.height || 'unknown',\n          filesize: format.filesize,\n          format_note: format.format_note || '',\n          vcodec: format.vcodec,\n          acodec: format.acodec,\n          fps: format.fps,\n          resolution: format.resolution || `${format.width}x${format.height}` || 'unknown'\n        })) || [],\n        url: url,\n        webpage_url: videoInfo.webpage_url || url,\n        extractor: videoInfo.extractor || 'unknown'\n      };\n\n      return NextResponse.json({ \n        success: true, \n        data: extractedInfo \n      });\n\n    } catch (execError: any) {\n      console.error('yt-dlp execution error:', execError);\n      \n      if (execError.code === 'ETIMEDOUT') {\n        return NextResponse.json({ \n          error: 'Request timeout - the video source may be slow to respond' \n        }, { status: 408 });\n      }\n\n      return NextResponse.json({ \n        error: 'Failed to extract video information',\n        details: execError.message \n      }, { status: 500 });\n    }\n\n  } catch (error: any) {\n    console.error('API error:', error);\n    return NextResponse.json({ \n      error: 'Internal server error',\n      details: error.message \n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,YAAY,CAAA,GAAA,iGAAA,CAAA,YAAS,AAAD,EAAE,mHAAA,CAAA,OAAI;AAEzB,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,QAAQ,IAAI;QAElC,IAAI,CAAC,KAAK;YACR,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAkB,GAAG;gBAAE,QAAQ;YAAI;QACvE;QAEA,sBAAsB;QACtB,IAAI;YACF,IAAI,IAAI;QACV,EAAE,OAAM;YACN,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAqB,GAAG;gBAAE,QAAQ;YAAI;QAC1E;QAEA,0CAA0C;QAC1C,MAAM,UAAU,CAAC,4CAA4C,EAAE,IAAI,CAAC,CAAC;QAErE,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,UAAU,SAAS;gBAClD,SAAS;gBACT,WAAW,OAAO,OAAO,GAAG,cAAc;YAC5C;YAEA,IAAI,UAAU,CAAC,QAAQ;gBACrB,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;oBACP,SAAS;gBACX,GAAG;oBAAE,QAAQ;gBAAI;YACnB;YAEA,oCAAoC;YACpC,MAAM,YAAY,KAAK,KAAK,CAAC;YAE7B,+BAA+B;YAC/B,MAAM,gBAAgB;gBACpB,OAAO,UAAU,KAAK,IAAI;gBAC1B,aAAa,UAAU,WAAW,IAAI;gBACtC,UAAU,UAAU,QAAQ,IAAI;gBAChC,UAAU,UAAU,QAAQ,IAAI;gBAChC,aAAa,UAAU,WAAW,IAAI;gBACtC,YAAY,UAAU,UAAU,IAAI;gBACpC,WAAW,UAAU,SAAS,IAAI;gBAClC,SAAS,UAAU,OAAO,EAAE,IAAI,CAAC,SAAgB,CAAC;wBAChD,WAAW,OAAO,SAAS;wBAC3B,KAAK,OAAO,GAAG;wBACf,SAAS,OAAO,OAAO,IAAI,OAAO,MAAM,IAAI;wBAC5C,UAAU,OAAO,QAAQ;wBACzB,aAAa,OAAO,WAAW,IAAI;wBACnC,QAAQ,OAAO,MAAM;wBACrB,QAAQ,OAAO,MAAM;wBACrB,KAAK,OAAO,GAAG;wBACf,YAAY,OAAO,UAAU,IAAI,GAAG,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO,MAAM,EAAE,IAAI;oBACzE,CAAC,MAAM,EAAE;gBACT,KAAK;gBACL,aAAa,UAAU,WAAW,IAAI;gBACtC,WAAW,UAAU,SAAS,IAAI;YACpC;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;YACR;QAEF,EAAE,OAAO,WAAgB;YACvB,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,IAAI,UAAU,IAAI,KAAK,aAAa;gBAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;gBACP,SAAS,UAAU,OAAO;YAC5B,GAAG;gBAAE,QAAQ;YAAI;QACnB;IAEF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,SAAS,MAAM,OAAO;QACxB,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}